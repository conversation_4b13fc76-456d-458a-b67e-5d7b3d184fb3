{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "onegate",
            "cwd": "apps/flutter/onegate",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "onegate (profile mode)",
            "cwd": "apps/flutter/onegate",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "onegate (release mode)",
            "cwd": "apps/flutter/onegate",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "auth",
            "cwd": "libs/flutter/auth",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "auth (profile mode)",
            "cwd": "libs/flutter/auth",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "auth (release mode)",
            "cwd": "libs/flutter/auth",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "common-widgets",
            "cwd": "libs/flutter/common/common-widgets",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "common-widgets (profile mode)",
            "cwd": "libs/flutter/common/common-widgets",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "common-widgets (release mode)",
            "cwd": "libs/flutter/common/common-widgets",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "one-theme",
            "cwd": "libs/flutter/common/one-theme",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "one-theme (profile mode)",
            "cwd": "libs/flutter/common/one-theme",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "one-theme (release mode)",
            "cwd": "libs/flutter/common/one-theme",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "retrofit",
            "cwd": "libs/flutter/common/retrofit",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "retrofit (profile mode)",
            "cwd": "libs/flutter/common/retrofit",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "retrofit (release mode)",
            "cwd": "libs/flutter/common/retrofit",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}