<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res"><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/mipmap-mdpi/ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-xxhdpi/android12splash.png" qualifiers="night-xxhdpi-v8" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-xxhdpi/splash.png" qualifiers="night-xxhdpi-v8" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/mipmap-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/values-night-v31/styles.xml" qualifiers="night-v31"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:windowSplashScreenBackground">#121212</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/android12splash</item>
        <item name="android:windowSplashScreenIconBackgroundColor">#121212</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="background" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night/background.png" qualifiers="night-v8" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night/launch_background.xml" qualifiers="night-v8" type="drawable"/><file name="background" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable/background.png" qualifiers="" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable/launch_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/mipmap-xxxhdpi/ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/mipmap-xxhdpi/ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-mdpi/android12splash.png" qualifiers="night-mdpi-v8" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-mdpi/splash.png" qualifiers="night-mdpi-v8" type="drawable"/><file path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/values-night/styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="background" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-v21/background.png" qualifiers="night-v21" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-v21/launch_background.xml" qualifiers="night-v21" type="drawable"/><file path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/values/colors.xml" qualifiers=""><color name="ic_launcher_background">#FFACAC</color></file><file path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/values/styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/values/strings.xml" qualifiers=""><string name="one_gate">My App Dev</string></file><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-hdpi/android12splash.png" qualifiers="night-hdpi-v8" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-hdpi/splash.png" qualifiers="night-hdpi-v8" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xhdpi/ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="drawable"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xhdpi/android12splash.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xhdpi/splash.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xhdpi/ic_launcher.png" qualifiers="xhdpi-v4" type="drawable"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-xhdpi/android12splash.png" qualifiers="night-xhdpi-v8" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-xhdpi/splash.png" qualifiers="night-xhdpi-v8" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xxhdpi/ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xxhdpi/android12splash.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xxhdpi/splash.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xxhdpi/ic_launcher.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-hdpi/ic_launcher_foreground.png" qualifiers="hdpi-v4" type="drawable"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-hdpi/android12splash.png" qualifiers="hdpi-v4" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-hdpi/splash.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-hdpi/ic_launcher.png" qualifiers="hdpi-v4" type="drawable"/><file name="background" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-v21/background.png" qualifiers="v21" type="drawable"/><file name="launch_background" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-v21/launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-mdpi/ic_launcher_foreground.png" qualifiers="mdpi-v4" type="drawable"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-mdpi/android12splash.png" qualifiers="mdpi-v4" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-mdpi/splash.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-mdpi/ic_launcher.png" qualifiers="mdpi-v4" type="drawable"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-xxxhdpi/android12splash.png" qualifiers="night-xxxhdpi-v8" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-night-xxxhdpi/splash.png" qualifiers="night-xxxhdpi-v8" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/mipmap-xhdpi/ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xxxhdpi/ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="android12splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xxxhdpi/android12splash.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="splash" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xxxhdpi/splash.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/drawable-xxxhdpi/ic_launcher.png" qualifiers="xxxhdpi-v4" type="drawable"/><file path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/values-v31/styles.xml" qualifiers="v31"><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:windowSplashScreenBackground">#FFACAC</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/android12splash</item>
        <item name="android:windowSplashScreenIconBackgroundColor">#FFACAC</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file name="ic_launcher" path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/main/res/mipmap-anydpi-v26/ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>