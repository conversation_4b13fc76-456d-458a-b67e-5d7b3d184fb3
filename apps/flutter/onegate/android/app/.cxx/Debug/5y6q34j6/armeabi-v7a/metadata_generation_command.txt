                        -H/Users/<USER>/Desktop/Futurescape/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON><PERSON>ID_PLATFORM=android-23
-<PERSON><PERSON><PERSON>OID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/29.0.13113456
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/29.0.13113456
-<PERSON><PERSON><PERSON>_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/29.0.13113456/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/build/app/intermediates/cxx/Debug/5y6q34j6/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/build/app/intermediates/cxx/Debug/5y6q34j6/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/5y6q34j6/armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2