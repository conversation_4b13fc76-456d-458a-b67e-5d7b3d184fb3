{"buildFiles": ["/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Developer/FS/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/5p626s1g/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Developer/FS/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/5p626s1g/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}