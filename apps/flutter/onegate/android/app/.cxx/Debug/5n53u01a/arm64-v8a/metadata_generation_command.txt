                        -H/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON><PERSON><PERSON>_PLATFORM=android-23
-D<PERSON>DROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125
-<PERSON><PERSON><PERSON>_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.31.6/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/build/app/intermediates/cxx/Debug/5n53u01a/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/build/app/intermediates/cxx/Debug/5n53u01a/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/5n53u01a/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2