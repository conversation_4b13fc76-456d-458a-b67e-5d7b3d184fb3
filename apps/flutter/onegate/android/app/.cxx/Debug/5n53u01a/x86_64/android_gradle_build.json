{"buildFiles": ["/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.31.6/bin/ninja", "-C", "/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/5n53u01a/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.31.6/bin/ninja", "-C", "/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/5n53u01a/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}