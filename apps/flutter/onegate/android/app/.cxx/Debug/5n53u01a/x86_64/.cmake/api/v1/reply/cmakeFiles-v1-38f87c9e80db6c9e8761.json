{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Determine.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Determine.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": ".cxx/Debug/5n53u01a/x86_64/CMakeFiles/3.31.5/CMakeSystem.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Initialize.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linux-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Determine-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android/Determine-Compiler.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Determine-Compiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Cray<PERSON>lang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": ".cxx/Debug/5n53u01a/x86_64/CMakeFiles/3.31.5/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android/Determine-Compiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Cray<PERSON>lang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": ".cxx/Debug/5n53u01a/x86_64/CMakeFiles/3.31.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Clang.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/flags.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": ".cxx/Debug/5n53u01a/x86_64/CMakeFiles/3.31.5/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/LLD-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Android-LLD-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-CXX-CXXImportStd.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": ".cxx/Debug/5n53u01a/x86_64/CMakeFiles/3.31.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/LLD-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Android-LLD-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/5n53u01a/x86_64", "source": "/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app"}, "version": {"major": 1, "minor": 1}}