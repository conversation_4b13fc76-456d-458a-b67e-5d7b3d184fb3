{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/bin/cmake", "cpack": "/opt/homebrew/bin/cpack", "ctest": "/opt/homebrew/bin/ctest", "root": "/opt/homebrew/share/cmake"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 5, "string": "3.31.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-26d3888dc32e7a7a100d.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-8bfccd99ad4ab45ebe9d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-dfb8994c2895606f5a97.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-8bfccd99ad4ab45ebe9d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-dfb8994c2895606f5a97.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-26d3888dc32e7a7a100d.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}}