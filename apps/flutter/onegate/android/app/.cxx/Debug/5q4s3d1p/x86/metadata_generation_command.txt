                        -H/home/<USER>/Documents/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-<PERSON><PERSON><PERSON><PERSON><PERSON>_PLATFORM=android-23
-<PERSON><PERSON><PERSON>OID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/home/<USER>/Android/Sdk/ndk/26.1.10909125
-DCMAKE_ANDROID_NDK=/home/<USER>/Android/Sdk/ndk/26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=/home/<USER>/Android/Sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/home/<USER>/Android/Sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/home/<USER>/Documents/one-monorepo/apps/flutter/onegate/build/app/intermediates/cxx/Debug/5q4s3d1p/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/home/<USER>/Documents/one-monorepo/apps/flutter/onegate/build/app/intermediates/cxx/Debug/5q4s3d1p/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-B/home/<USER>/Documents/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/5q4s3d1p/x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2