{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/703s6y2b/armeabi-v7a", "source": "/Users/<USER>/Downloads/flutter_2/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}