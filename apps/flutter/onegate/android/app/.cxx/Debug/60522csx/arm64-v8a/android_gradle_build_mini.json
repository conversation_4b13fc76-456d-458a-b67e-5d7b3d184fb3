{"buildFiles": ["/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Developer/fs/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/60522csx/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Developer/fs/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/60522csx/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}