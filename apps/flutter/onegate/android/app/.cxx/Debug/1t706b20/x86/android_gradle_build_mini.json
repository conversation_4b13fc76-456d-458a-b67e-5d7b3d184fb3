{"buildFiles": ["/Users/<USER>/Desktop/Futurescape/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/1t706b20/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/1t706b20/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}