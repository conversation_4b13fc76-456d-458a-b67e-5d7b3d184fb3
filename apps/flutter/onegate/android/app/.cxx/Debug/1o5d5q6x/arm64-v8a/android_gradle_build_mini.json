{"buildFiles": ["/Users/<USER>/Downloads/flutter_2/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/1o5d5q6x/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/one-monorepo/apps/flutter/onegate/android/app/.cxx/Debug/1o5d5q6x/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}