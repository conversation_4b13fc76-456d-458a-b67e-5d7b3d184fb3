{"buildFiles": ["/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Developer/fs/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/3hq62353/x86_64", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Developer/fs/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/3hq62353/x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}