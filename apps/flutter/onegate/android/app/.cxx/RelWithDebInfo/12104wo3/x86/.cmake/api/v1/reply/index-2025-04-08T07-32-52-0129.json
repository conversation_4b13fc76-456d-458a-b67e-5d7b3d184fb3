{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/bin/cmake", "cpack": "/opt/homebrew/bin/cpack", "ctest": "/opt/homebrew/bin/ctest", "root": "/opt/homebrew/share/cmake"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 5, "string": "3.31.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-b91fb8e25e7858f8d62d.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-9583c97a4694a7aa768f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-608a94876cd872312ed1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-9583c97a4694a7aa768f.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-608a94876cd872312ed1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-b91fb8e25e7858f8d62d.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}}