{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": ".cxx/RelWithDebInfo/12104wo3/x86/CMakeFiles/3.31.5/CMakeSystem.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/platforms.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Initialize.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linux-Initialize.cmake"}, {"isGenerated": true, "path": ".cxx/RelWithDebInfo/12104wo3/x86/CMakeFiles/3.31.5/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": ".cxx/RelWithDebInfo/12104wo3/x86/CMakeFiles/3.31.5/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Clang.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake"}, {"isExternal": true, "path": "/Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/flags.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/LLD-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Android-LLD-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Android-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/LLD-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Linker/LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Android-LLD-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/12104wo3/x86", "source": "/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app"}, "version": {"major": 1, "minor": 1}}