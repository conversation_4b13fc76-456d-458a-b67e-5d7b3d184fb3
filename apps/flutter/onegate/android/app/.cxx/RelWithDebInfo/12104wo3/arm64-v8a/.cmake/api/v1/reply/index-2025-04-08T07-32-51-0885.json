{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/opt/homebrew/bin/cmake", "cpack": "/opt/homebrew/bin/cpack", "ctest": "/opt/homebrew/bin/ctest", "root": "/opt/homebrew/share/cmake"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 5, "string": "3.31.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-164fa83cc3c00ee63ef8.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-d035bb929c16f55656f7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-317b8eb274416c892f12.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-d035bb929c16f55656f7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-317b8eb274416c892f12.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-164fa83cc3c00ee63ef8.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}}