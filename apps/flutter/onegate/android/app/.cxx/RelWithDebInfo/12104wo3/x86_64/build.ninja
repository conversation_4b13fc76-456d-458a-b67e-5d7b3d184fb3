# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.31

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/12104wo3/x86_64/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/12104wo3/x86_64 && /opt/homebrew/bin/ccmake -S/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app -B/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/12104wo3/x86_64
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/12104wo3/x86_64 && /opt/homebrew/bin/cmake --regenerate-during-build -S/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app -B/Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/12104wo3/x86_64
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/.cxx/RelWithDebInfo/12104wo3/x86_64

build all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | /Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/CMakeLists.txt /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/platforms.cmake /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang-CXX.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Linker/GNU.cmake /opt/homebrew/share/cmake/Modules/Linker/LLD-C.cmake /opt/homebrew/share/cmake/Modules/Linker/LLD-CXX.cmake /opt/homebrew/share/cmake/Modules/Linker/LLD.cmake /opt/homebrew/share/cmake/Modules/Platform/Android-Clang-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Android-Clang-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Android-Clang.cmake /opt/homebrew/share/cmake/Modules/Platform/Android-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Android.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Android-LLD-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Android-LLD-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/GNU.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD.cmake /opt/homebrew/share/cmake/Modules/Platform/Linux-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Linux.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeCCompiler.cmake CMakeFiles/3.31.5/CMakeCXXCompiler.cmake CMakeFiles/3.31.5/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build /Users/<USER>/Desktop/Futurescape/one-monorepo/apps/flutter/onegate/android/app/CMakeLists.txt /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/flags.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake /Users/<USER>/Library/Android/sdk/ndk/26.1.10909125/build/cmake/platforms.cmake /opt/homebrew/share/cmake/Modules/CMakeCInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeCXXInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeCommonLanguageInclude.cmake /opt/homebrew/share/cmake/Modules/CMakeGenericSystem.cmake /opt/homebrew/share/cmake/Modules/CMakeInitializeConfigs.cmake /opt/homebrew/share/cmake/Modules/CMakeLanguageInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake /opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake /opt/homebrew/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang-C.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang-CXX.cmake /opt/homebrew/share/cmake/Modules/Compiler/Clang.cmake /opt/homebrew/share/cmake/Modules/Compiler/GNU.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake /opt/homebrew/share/cmake/Modules/Linker/GNU.cmake /opt/homebrew/share/cmake/Modules/Linker/LLD-C.cmake /opt/homebrew/share/cmake/Modules/Linker/LLD-CXX.cmake /opt/homebrew/share/cmake/Modules/Linker/LLD.cmake /opt/homebrew/share/cmake/Modules/Platform/Android-Clang-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Android-Clang-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Android-Clang.cmake /opt/homebrew/share/cmake/Modules/Platform/Android-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Android.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Android-LLD-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Android-LLD-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/GNU.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-GNU.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD-C.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD-CXX.cmake /opt/homebrew/share/cmake/Modules/Platform/Linker/Linux-LLD.cmake /opt/homebrew/share/cmake/Modules/Platform/Linux-Initialize.cmake /opt/homebrew/share/cmake/Modules/Platform/Linux.cmake /opt/homebrew/share/cmake/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.31.5/CMakeCCompiler.cmake CMakeFiles/3.31.5/CMakeCXXCompiler.cmake CMakeFiles/3.31.5/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
