{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984c86594d699922644f48b32cb8b06d5e", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_appauth", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_appauth", "INFOPLIST_FILE": "Target Support Files/flutter_appauth/ResourceBundle-flutter_appauth_privacy-flutter_appauth-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "flutter_appauth_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98301a98ed492565e8cab267b7e750b006", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a6243d2e4c04d40dc4a5e2407e16779e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_appauth", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_appauth", "INFOPLIST_FILE": "Target Support Files/flutter_appauth/ResourceBundle-flutter_appauth_privacy-flutter_appauth-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "flutter_appauth_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9825bd3893d8ae5a16f94580a5a416f972", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a6243d2e4c04d40dc4a5e2407e16779e", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/flutter_appauth", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "flutter_appauth", "INFOPLIST_FILE": "Target Support Files/flutter_appauth/ResourceBundle-flutter_appauth_privacy-flutter_appauth-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "PRODUCT_NAME": "flutter_appauth_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98be6cf6688ad3b84fdd943b65c50880fe", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d9e338d0179a99df3a138658f2653419", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9827df11c90f0c990c20a3c90d69bf010a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989b0607eb1cc04644f948595fe2f73df5", "guid": "bfdfe7dc352907fc980b868725387e983849ab7dea329ad793d1bda7a8c7f3da"}], "guid": "bfdfe7dc352907fc980b868725387e986f673e284ca4f8d9763b9e048ac49d9a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9838116aba79bee5cd919637253bcf2ecc", "name": "flutter_appauth-flutter_appauth_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982460b3be5f4208d02013fc970dac2dce", "name": "flutter_appauth_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}