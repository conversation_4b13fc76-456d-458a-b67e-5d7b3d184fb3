{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3dc79f05ebd32cad2e017657550a0ad", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter_2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter_2/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e46b71971fbceaf39504fa84a5e8076", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter_2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter_2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986e46b71971fbceaf39504fa84a5e8076", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Downloads/flutter_2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Downloads/flutter_2/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fdbb614091750f139c26d63073824e97", "guid": "bfdfe7dc352907fc980b868725387e983e7975d624b942f98f598fdb846bd9b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988215d21051d167532737616c14ac35ac", "guid": "bfdfe7dc352907fc980b868725387e984dd45bee2fb955c5a16b0a05acfaae09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f4f112145a6409ccba27c599e5fd0c", "guid": "bfdfe7dc352907fc980b868725387e9836658739f0063a897851d83437019423", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d82507d69fa67abbb9e28f93fbe27960", "guid": "bfdfe7dc352907fc980b868725387e98da787134d0edb546a3c86f266121687c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0063d0c10ae64ae2592e8d8d3537247", "guid": "bfdfe7dc352907fc980b868725387e9826bf245dccf93af166466ca2f32c27a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ede24b0d2f8955c297791cf8b3cc622", "guid": "bfdfe7dc352907fc980b868725387e989cf2740e812deaa5389ff3048ee72b1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b90315830529e66f2472f66db3abde96", "guid": "bfdfe7dc352907fc980b868725387e989980572a5a7bbc253d9760b3232b6dd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b2a9f53d73701c36e3cabdbe4533532", "guid": "bfdfe7dc352907fc980b868725387e989dd64dd824bfc2d78b50277ac3c7b23e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865fd4cf7c0b5d0ef8aaf010930bdf154", "guid": "bfdfe7dc352907fc980b868725387e9801924b140f2a4bc03382646dad2b04b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815e849d1c89ffa6a3c99056148764acd", "guid": "bfdfe7dc352907fc980b868725387e9829e746092a8516ccbb0d304c86ed53a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ee125aa12a2b290f678b86ffdb4f97b", "guid": "bfdfe7dc352907fc980b868725387e9823f3f44be0ac2520e0c951586cace904", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817a688df3b9902aacd1b914ba4a05da5", "guid": "bfdfe7dc352907fc980b868725387e9829a96f2df88da4e47c5ff4c73db9389f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ccfdf9646f209dcd932719dca4f474b", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3e75c222e36de5f25a39f92bfd5f000", "guid": "bfdfe7dc352907fc980b868725387e982cc757ccc5e224da5699ee85ad649f41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c499b9d26c9b2b737fa62d25cae5c577", "guid": "bfdfe7dc352907fc980b868725387e9899d59693ca751870a7af0140a23bca56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865e217dc986ec714ab46eacb37665a16", "guid": "bfdfe7dc352907fc980b868725387e983b03e176c2aafa0d870d004b5c863142", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40b1813108700f531abe631cb436f2d", "guid": "bfdfe7dc352907fc980b868725387e98e0af7e0370841107a21e7487b4e918df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ef395a5dfd35a7fcfef57e1e344d13b", "guid": "bfdfe7dc352907fc980b868725387e982bf88c76d892bcfa28f517efd9d1a6ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d22c24157432d85725518c7684d3caee", "guid": "bfdfe7dc352907fc980b868725387e98ee1df7cfe474421b69472ded2d3487f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987714d46d330a222e39a8d1fd91e13cf5", "guid": "bfdfe7dc352907fc980b868725387e98bbefeef9e3b1b5f7baf04cb65e522708", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de70e3e14d15c3aee7c1b04517b1fc51", "guid": "bfdfe7dc352907fc980b868725387e98dbe5b49cb71f3f19a5943ecd2f28e21f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880e6a89f898976b430c92759e1d0cb31", "guid": "bfdfe7dc352907fc980b868725387e98505e9688a47fe025b24a3e7ffe8548db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f84ec4e2e9d6cc54c2962354ba1fa953", "guid": "bfdfe7dc352907fc980b868725387e98a858e969a186f326b468507cc3efead5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a86aee98da2a798783e7461a0f21aaa2", "guid": "bfdfe7dc352907fc980b868725387e98f371e047a7525905ff6958c4d12d8d07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f9a3680d9c7eebb1b92564eccddb7ac", "guid": "bfdfe7dc352907fc980b868725387e98131317dbc1f11ee8e37e394a430dd719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1a1386b197e9379b92d4c47a80f8d44", "guid": "bfdfe7dc352907fc980b868725387e98653864c1a27c27fc53b845c0bc01d2a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbec99081313e33d9bd267b7836c7461", "guid": "bfdfe7dc352907fc980b868725387e98f7072143b1a8651ba7a318494b4238fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e7634358e7e64d128ee5fef30c80070", "guid": "bfdfe7dc352907fc980b868725387e98321f723071b061d7c50afd3a40e0f2b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98574c81e451a4a628f7bf30b98deb2336", "guid": "bfdfe7dc352907fc980b868725387e98cdd5a739a7092cd995a743daa5d56ea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db56e7be0ba799faa1bc8f509d90ca1d", "guid": "bfdfe7dc352907fc980b868725387e98333e5dc3eed90ab626feb99602c5a935"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981139dfc202b5f8bfec0c7b77fb95feb5", "guid": "bfdfe7dc352907fc980b868725387e98fa7998e0f4a92566412122743d86f153"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876845e8a75af8ca815edb447dd579af2", "guid": "bfdfe7dc352907fc980b868725387e98353952046cb444d1d7d830454392819c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896fbe88cd9de98af8cb57019c4e67d0c", "guid": "bfdfe7dc352907fc980b868725387e98da917d87d9b7e8dff1e76a083f85bb19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb51f2105de88800cba29ad7fa9c7b7e", "guid": "bfdfe7dc352907fc980b868725387e982f3150af8a5607d077657469f641533e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b40aaec3cb959a2aa7f067a33dfc8b8", "guid": "bfdfe7dc352907fc980b868725387e9844565f405a0accac234c135c41b7d119"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6878229f72efbec8265dc07dbb4f449", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98925a241badde470fd7bf747a193635bf", "guid": "bfdfe7dc352907fc980b868725387e98f4220558059df563553d655e8f0d2696"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838eb171439f81a58b46cfc29f29afbe5", "guid": "bfdfe7dc352907fc980b868725387e98da0c4abce0197f7352dddf4803aa3775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abab9eac6c407325739fe4628f716a0a", "guid": "bfdfe7dc352907fc980b868725387e984337b48910d90c03a3a87227423ed8d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882a448079165e9f9729cc95c12ae3f9b", "guid": "bfdfe7dc352907fc980b868725387e98edb7cfd876e93219f5aa2ee580355e03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdf126215e13e8a017cabb170e45ec8f", "guid": "bfdfe7dc352907fc980b868725387e98476b921a112614a3e2b6afd6142c4ad6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e71b95d4fc0670863c25719cee03ce8", "guid": "bfdfe7dc352907fc980b868725387e9817f5da0e45013159f4f9db6ba4a1bcdf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858e0088d1fbd10aee0bffac50a6d2b2f", "guid": "bfdfe7dc352907fc980b868725387e980843057b6dfa5c38399b6cbc9764252b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838dd6e56dd38ca39d254e733a06ec536", "guid": "bfdfe7dc352907fc980b868725387e98a45385915695235afbc3084f9164dbcd"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98716008d53d8da7fa05403503bab0f2af", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}