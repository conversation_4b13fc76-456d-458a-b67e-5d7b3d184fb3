<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>tel</string>
		</array>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>We need your location to provide better services when the app is in use.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>We need your location to provide better services even in the background.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>We need your location to provide better services, whether the app is in the foreground or background.</string>
		<key>NSSpeechRecognitionUsageDescription</key>
		<string>This app requires speech recognition to provide voice interaction features.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>This app needs access to your microphone for voice input.</string>
		<key>NSCameraUsageDescription</key>
		<string>This app requires camera access to capture images or videos.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>We need access to your photo library to upload or share images.</string>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Flutter Onegate</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>onegate</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UIBackgroundModes</key>
		<array>
			<string>location</string>
		</array>
		<!-- AppAuth URL Scheme Configuration -->
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLName</key>
				<string>com.cubeonebiz.gate.oauth</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.cubeonebiz.gate</string>
				</array>
			</dict>
		</array>
	</dict>
</plist>
