name: flutter_onegate
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  device_preview: ^1.2.0
  page_transition: ^2.0.9
  lottie: ^2.6.0
  bloc: ^8.1.2
  flutter_bloc: ^8.1.3
  get_it: ^7.6.0
  dio: ^5.8.0
  kiosk_mode: ^0.6.0
  shared_preferences: ^2.5.3
  animated_text_kit: ^4.2.2
  material_symbols_icons: ^4.2668.0
  fluttertoast: ^8.2.10
  badges: ^3.1.2
  chips_choice: ^3.0.0
  toggle_switch: ^2.1.0
  country_code_picker: ^3.0.0
  email_validator: ^2.1.17
  flutter_native_splash: ^2.4.1
  cached_network_image: ^3.4.1
  image_picker: ^1.0.4
  path_provider: ^2.1.4
  concentric_transition: ^1.0.3
  flutter_animated_button: ^2.0.3
  serverpod_flutter: ^1.2.0
  onegate_client:
    path: ../serverpod/onegate/onegate_client
  one_gate:
    path: ../../../libs/one-gate
  random_avatar: ^0.0.8
  animations: ^2.0.8
  flutter_animate: ^4.2.0+1
  flutter_screenutil: ^5.9.0
  google_mlkit_face_detection: ^0.12.0
  easy_stepper: ^0.8.1
  carousel_slider: ^5.0.0
  numpad: ^0.0.2
  numpad_layout: ^0.0.3
  flutter_otp_text_field: ^1.1.1
  timezone: ^0.7.0
  ionicons: ^0.2.1
  flutter_appauth: ^7.0.0
  flutter_secure_storage: ^9.2.2
  permission_handler: ^11.3.1
  camera: ^0.11.0+2
  dart_amqp: ^0.3.1
  alarm: ^5.1.1
  url_launcher: ^6.3.1
  calendar_date_picker2: ^0.5.3
  flutter_dotenv: ^5.2.1
  equatable: ^2.0.7
  pinput: ^5.0.1
  infinite_scroll_pagination: ^4.1.0
  flutter_image_compress: ^2.4.0
  socket_io_client: ^3.0.2
  connectivity_plus: ^5.0.2
  qr_code_scanner_plus: ^2.0.10+1
  vibration: ^3.1.3
  flutter_kiosk_mode: ^0.0.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.2.0
  intl: ^0.19.0
  # Meilisearch for advanced search
  meilisearch: ^0.15.0
  # Additional utilities
  uuid: ^4.5.1
  # Crash reporting dependencies
  device_info_plus: ^11.3.3
  package_info_plus: ^8.1.0
  # Theme and state management
  one_theme:
    path: ../../../libs/flutter/common/one-theme
  common_widgets:
    path: ../../../libs/flutter/common/common-widgets
  provider: ^6.1.2
  # Additional dependencies for crash reporting
  shimmer: ^3.0.0
  meta: ^1.16.0
  # No background task dependencies needed

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4
  flutter_lints: ^2.0.0
  speech_to_text: ^6.3.0
  build_runner: ^2.4.8
  hive_generator: ^2.0.1

dependency_overrides:
  speech_to_text: ^7.0.0
  device_info_plus: ^11.3.3
  meta: ^1.16.0



flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
    - assets/
    - assets/media/images/
    - assets/onegate/theme.json
    - assets/json/gates_mock.json
    - assets/json/media.json
    - assets/json/idcard.json
    - assets/json/approved.json
    - assets/media/audio/alarm.mp3
    - assets/json/no_internet.json
    - assets/media/images/person-icon-1670.png
    - shorebird.yaml

flutter_native_splash:
  color: "#FFACAC"
  image: assets/media/images/oneapp_logo.png
  color_dark: "#121212"
  image_dark: assets/media/images/oneapp_logo.png

  android: true
  ios: true
  web: false

  android_gravity: center
  ios_content_mode: center

  # Use branding instead of image for Android 12+
  branding: assets/media/images/oneapp_logo.png
  branding_dark: assets/media/images/oneapp_logo.png

  # Android 12 configs
  android_12:
    # Set these to null to use the branding instead
    image: null
    icon_background_color: "#FFACAC"
    image_dark: null
    icon_background_color_dark: "#121212"