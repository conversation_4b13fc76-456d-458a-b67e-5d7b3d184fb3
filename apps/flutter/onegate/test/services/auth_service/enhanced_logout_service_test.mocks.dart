// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in flutter_onegate/test/services/auth_service/enhanced_logout_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:io' as _i8;

import 'package:flutter/foundation.dart' as _i6;
import 'package:flutter_appauth/src/flutter_appauth.dart' as _i4;
import 'package:flutter_appauth_platform_interface/flutter_appauth_platform_interface.dart'
    as _i2;
import 'package:flutter_onegate/data/datasources/gate_storage.dart' as _i7;
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthorizationTokenResponse_0 extends _i1.SmartFake
    implements _i2.AuthorizationTokenResponse {
  _FakeAuthorizationTokenResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAuthorizationResponse_1 extends _i1.SmartFake
    implements _i2.AuthorizationResponse {
  _FakeAuthorizationResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTokenResponse_2 extends _i1.SmartFake implements _i2.TokenResponse {
  _FakeTokenResponse_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeEndSessionResponse_3 extends _i1.SmartFake
    implements _i2.EndSessionResponse {
  _FakeEndSessionResponse_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeIOSOptions_4 extends _i1.SmartFake implements _i3.IOSOptions {
  _FakeIOSOptions_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAndroidOptions_5 extends _i1.SmartFake
    implements _i3.AndroidOptions {
  _FakeAndroidOptions_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLinuxOptions_6 extends _i1.SmartFake implements _i3.LinuxOptions {
  _FakeLinuxOptions_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWindowsOptions_7 extends _i1.SmartFake
    implements _i3.WindowsOptions {
  _FakeWindowsOptions_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWebOptions_8 extends _i1.SmartFake implements _i3.WebOptions {
  _FakeWebOptions_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMacOsOptions_9 extends _i1.SmartFake implements _i3.MacOsOptions {
  _FakeMacOsOptions_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [FlutterAppAuth].
///
/// See the documentation for Mockito's code generation for more information.
class MockFlutterAppAuth extends _i1.Mock implements _i4.FlutterAppAuth {
  MockFlutterAppAuth() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.AuthorizationTokenResponse> authorizeAndExchangeCode(
          _i2.AuthorizationTokenRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #authorizeAndExchangeCode,
          [request],
        ),
        returnValue: _i5.Future<_i2.AuthorizationTokenResponse>.value(
            _FakeAuthorizationTokenResponse_0(
          this,
          Invocation.method(
            #authorizeAndExchangeCode,
            [request],
          ),
        )),
      ) as _i5.Future<_i2.AuthorizationTokenResponse>);

  @override
  _i5.Future<_i2.AuthorizationResponse> authorize(
          _i2.AuthorizationRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #authorize,
          [request],
        ),
        returnValue: _i5.Future<_i2.AuthorizationResponse>.value(
            _FakeAuthorizationResponse_1(
          this,
          Invocation.method(
            #authorize,
            [request],
          ),
        )),
      ) as _i5.Future<_i2.AuthorizationResponse>);

  @override
  _i5.Future<_i2.TokenResponse> token(_i2.TokenRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #token,
          [request],
        ),
        returnValue: _i5.Future<_i2.TokenResponse>.value(_FakeTokenResponse_2(
          this,
          Invocation.method(
            #token,
            [request],
          ),
        )),
      ) as _i5.Future<_i2.TokenResponse>);

  @override
  _i5.Future<_i2.EndSessionResponse> endSession(
          _i2.EndSessionRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #endSession,
          [request],
        ),
        returnValue:
            _i5.Future<_i2.EndSessionResponse>.value(_FakeEndSessionResponse_3(
          this,
          Invocation.method(
            #endSession,
            [request],
          ),
        )),
      ) as _i5.Future<_i2.EndSessionResponse>);
}

/// A class which mocks [FlutterSecureStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockFlutterSecureStorage extends _i1.Mock
    implements _i3.FlutterSecureStorage {
  MockFlutterSecureStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.IOSOptions get iOptions => (super.noSuchMethod(
        Invocation.getter(#iOptions),
        returnValue: _FakeIOSOptions_4(
          this,
          Invocation.getter(#iOptions),
        ),
      ) as _i3.IOSOptions);

  @override
  _i3.AndroidOptions get aOptions => (super.noSuchMethod(
        Invocation.getter(#aOptions),
        returnValue: _FakeAndroidOptions_5(
          this,
          Invocation.getter(#aOptions),
        ),
      ) as _i3.AndroidOptions);

  @override
  _i3.LinuxOptions get lOptions => (super.noSuchMethod(
        Invocation.getter(#lOptions),
        returnValue: _FakeLinuxOptions_6(
          this,
          Invocation.getter(#lOptions),
        ),
      ) as _i3.LinuxOptions);

  @override
  _i3.WindowsOptions get wOptions => (super.noSuchMethod(
        Invocation.getter(#wOptions),
        returnValue: _FakeWindowsOptions_7(
          this,
          Invocation.getter(#wOptions),
        ),
      ) as _i3.WindowsOptions);

  @override
  _i3.WebOptions get webOptions => (super.noSuchMethod(
        Invocation.getter(#webOptions),
        returnValue: _FakeWebOptions_8(
          this,
          Invocation.getter(#webOptions),
        ),
      ) as _i3.WebOptions);

  @override
  _i3.MacOsOptions get mOptions => (super.noSuchMethod(
        Invocation.getter(#mOptions),
        returnValue: _FakeMacOsOptions_9(
          this,
          Invocation.getter(#mOptions),
        ),
      ) as _i3.MacOsOptions);

  @override
  void registerListener({
    required String? key,
    required _i6.ValueChanged<String?>? listener,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #registerListener,
          [],
          {
            #key: key,
            #listener: listener,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unregisterListener({
    required String? key,
    required _i6.ValueChanged<String?>? listener,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #unregisterListener,
          [],
          {
            #key: key,
            #listener: listener,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unregisterAllListenersForKey({required String? key}) =>
      super.noSuchMethod(
        Invocation.method(
          #unregisterAllListenersForKey,
          [],
          {#key: key},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void unregisterAllListeners() => super.noSuchMethod(
        Invocation.method(
          #unregisterAllListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<void> write({
    required String? key,
    required String? value,
    _i3.IOSOptions? iOptions,
    _i3.AndroidOptions? aOptions,
    _i3.LinuxOptions? lOptions,
    _i3.WebOptions? webOptions,
    _i3.MacOsOptions? mOptions,
    _i3.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #write,
          [],
          {
            #key: key,
            #value: value,
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> read({
    required String? key,
    _i3.IOSOptions? iOptions,
    _i3.AndroidOptions? aOptions,
    _i3.LinuxOptions? lOptions,
    _i3.WebOptions? webOptions,
    _i3.MacOsOptions? mOptions,
    _i3.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #read,
          [],
          {
            #key: key,
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<bool> containsKey({
    required String? key,
    _i3.IOSOptions? iOptions,
    _i3.AndroidOptions? aOptions,
    _i3.LinuxOptions? lOptions,
    _i3.WebOptions? webOptions,
    _i3.MacOsOptions? mOptions,
    _i3.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #containsKey,
          [],
          {
            #key: key,
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> delete({
    required String? key,
    _i3.IOSOptions? iOptions,
    _i3.AndroidOptions? aOptions,
    _i3.LinuxOptions? lOptions,
    _i3.WebOptions? webOptions,
    _i3.MacOsOptions? mOptions,
    _i3.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #delete,
          [],
          {
            #key: key,
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, String>> readAll({
    _i3.IOSOptions? iOptions,
    _i3.AndroidOptions? aOptions,
    _i3.LinuxOptions? lOptions,
    _i3.WebOptions? webOptions,
    _i3.MacOsOptions? mOptions,
    _i3.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #readAll,
          [],
          {
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i5.Future<Map<String, String>>.value(<String, String>{}),
      ) as _i5.Future<Map<String, String>>);

  @override
  _i5.Future<void> deleteAll({
    _i3.IOSOptions? iOptions,
    _i3.AndroidOptions? aOptions,
    _i3.LinuxOptions? lOptions,
    _i3.WebOptions? webOptions,
    _i3.MacOsOptions? mOptions,
    _i3.WindowsOptions? wOptions,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteAll,
          [],
          {
            #iOptions: iOptions,
            #aOptions: aOptions,
            #lOptions: lOptions,
            #webOptions: webOptions,
            #mOptions: mOptions,
            #wOptions: wOptions,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool?> isCupertinoProtectedDataAvailable() => (super.noSuchMethod(
        Invocation.method(
          #isCupertinoProtectedDataAvailable,
          [],
        ),
        returnValue: _i5.Future<bool?>.value(),
      ) as _i5.Future<bool?>);
}

/// A class which mocks [GateStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockGateStorage extends _i1.Mock implements _i7.GateStorage {
  MockGateStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<void> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveFaceRecConfig(Map<String, dynamic>? config) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveFaceRecConfig,
          [config],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>?> getFaceRecConfig() => (super.noSuchMethod(
        Invocation.method(
          #getFaceRecConfig,
          [],
        ),
        returnValue: _i5.Future<Map<String, dynamic>?>.value(),
      ) as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<void> saveAccessToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #saveAccessToken,
          [token],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveRefreshToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #saveRefreshToken,
          [token],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveTokenExpiry(DateTime? expiryTime) => (super.noSuchMethod(
        Invocation.method(
          #saveTokenExpiry,
          [expiryTime],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveVisitorImageBase64(_i8.File? imageFile) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveVisitorImageBase64,
          [imageFile],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeVisitorImage() => (super.noSuchMethod(
        Invocation.method(
          #removeVisitorImage,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i8.File?> getVisitorImageBase64() => (super.noSuchMethod(
        Invocation.method(
          #getVisitorImageBase64,
          [],
        ),
        returnValue: _i5.Future<_i8.File?>.value(),
      ) as _i5.Future<_i8.File?>);

  @override
  _i5.Future<void> saveSocietyDetails(
    String? societyId,
    String? societyName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveSocietyDetails,
          [
            societyId,
            societyName,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveSocietyId(String? societyId) => (super.noSuchMethod(
        Invocation.method(
          #saveSocietyId,
          [societyId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getSocietyId() => (super.noSuchMethod(
        Invocation.method(
          #getSocietyId,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<String?> getAccessToken() => (super.noSuchMethod(
        Invocation.method(
          #getAccessToken,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<String?> getRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #getRefreshToken,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<bool> isTokenExpired() => (super.noSuchMethod(
        Invocation.method(
          #isTokenExpired,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> saveUserId(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #saveUserId,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getUserId() => (super.noSuchMethod(
        Invocation.method(
          #getUserId,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveUsername(String? username) => (super.noSuchMethod(
        Invocation.method(
          #saveUsername,
          [username],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveImage(String? image) => (super.noSuchMethod(
        Invocation.method(
          #saveImage,
          [image],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setComingFrom(String? comingFrom) => (super.noSuchMethod(
        Invocation.method(
          #setComingFrom,
          [comingFrom],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getComingFrom() => (super.noSuchMethod(
        Invocation.method(
          #getComingFrom,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> removeComingFrom() => (super.noSuchMethod(
        Invocation.method(
          #removeComingFrom,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getImage() => (super.noSuchMethod(
        Invocation.method(
          #getImage,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<String?> getUsername() => (super.noSuchMethod(
        Invocation.method(
          #getUsername,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveUserEmail(String? email) => (super.noSuchMethod(
        Invocation.method(
          #saveUserEmail,
          [email],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getUserEmail() => (super.noSuchMethod(
        Invocation.method(
          #getUserEmail,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveUserFullName(String? fullName) => (super.noSuchMethod(
        Invocation.method(
          #saveUserFullName,
          [fullName],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getUserFullName() => (super.noSuchMethod(
        Invocation.method(
          #getUserFullName,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveUserRoles(List<String>? roles) => (super.noSuchMethod(
        Invocation.method(
          #saveUserRoles,
          [roles],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<String>> getUserRoles() => (super.noSuchMethod(
        Invocation.method(
          #getUserRoles,
          [],
        ),
        returnValue: _i5.Future<List<String>>.value(<String>[]),
      ) as _i5.Future<List<String>>);

  @override
  _i5.Future<void> saveSessionTimestamp(DateTime? timestamp) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveSessionTimestamp,
          [timestamp],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<DateTime?> getSessionTimestamp() => (super.noSuchMethod(
        Invocation.method(
          #getSessionTimestamp,
          [],
        ),
        returnValue: _i5.Future<DateTime?>.value(),
      ) as _i5.Future<DateTime?>);

  @override
  _i5.Future<void> saveRole(String? role) => (super.noSuchMethod(
        Invocation.method(
          #saveRole,
          [role],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<dynamic, dynamic>> getSocietyDetails() => (super.noSuchMethod(
        Invocation.method(
          #getSocietyDetails,
          [],
        ),
        returnValue:
            _i5.Future<Map<dynamic, dynamic>>.value(<dynamic, dynamic>{}),
      ) as _i5.Future<Map<dynamic, dynamic>>);

  @override
  _i5.Future<String?> getRole() => (super.noSuchMethod(
        Invocation.method(
          #getRole,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveMemberDetails(Map<String, dynamic>? memberDetails) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveMemberDetails,
          [memberDetails],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>?> getMemberDetails() => (super.noSuchMethod(
        Invocation.method(
          #getMemberDetails,
          [],
        ),
        returnValue: _i5.Future<Map<String, dynamic>?>.value(),
      ) as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<void> clearStorage(String? key) => (super.noSuchMethod(
        Invocation.method(
          #clearStorage,
          [key],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearTokens() => (super.noSuchMethod(
        Invocation.method(
          #clearTokens,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveVisitorLogId(String? visitorLogId) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveVisitorLogId,
          [visitorLogId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getVisitorLogId() => (super.noSuchMethod(
        Invocation.method(
          #getVisitorLogId,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> clearVisitorLogId() => (super.noSuchMethod(
        Invocation.method(
          #clearVisitorLogId,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearVisitorImage() => (super.noSuchMethod(
        Invocation.method(
          #clearVisitorImage,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  bool? getTooglevalue(String? key) => (super.noSuchMethod(Invocation.method(
        #getTooglevalue,
        [key],
      )) as bool?);

  @override
  _i5.Future<void> setToogleValue(
    String? key,
    bool? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setToogleValue,
          [
            key,
            value,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveMemberList(List<dynamic>? memberList) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveMemberList,
          [memberList],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<dynamic>?> getMemberList() => (super.noSuchMethod(
        Invocation.method(
          #getMemberList,
          [],
        ),
        returnValue: _i5.Future<List<dynamic>?>.value(),
      ) as _i5.Future<List<dynamic>?>);

  @override
  _i5.Future<void> saveMemberApproval(bool? approval) => (super.noSuchMethod(
        Invocation.method(
          #saveMemberApproval,
          [approval],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool?> getMemberApproval() => (super.noSuchMethod(
        Invocation.method(
          #getMemberApproval,
          [],
        ),
        returnValue: _i5.Future<bool?>.value(),
      ) as _i5.Future<bool?>);

  @override
  _i5.Future<void> saveSelectedGate(
    String? gateName,
    String? gateId,
    String? gateType,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveSelectedGate,
          [
            gateName,
            gateId,
            gateType,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, String?>> getSelectedGate() => (super.noSuchMethod(
        Invocation.method(
          #getSelectedGate,
          [],
        ),
        returnValue:
            _i5.Future<Map<String, String?>>.value(<String, String?>{}),
      ) as _i5.Future<Map<String, String?>>);

  @override
  _i5.Future<String?> getGateType() => (super.noSuchMethod(
        Invocation.method(
          #getGateType,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> setMeilisearchHost(String? host) => (super.noSuchMethod(
        Invocation.method(
          #setMeilisearchHost,
          [host],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getMeilisearchHost() => (super.noSuchMethod(
        Invocation.method(
          #getMeilisearchHost,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> setMeilisearchApiKey(String? apiKey) => (super.noSuchMethod(
        Invocation.method(
          #setMeilisearchApiKey,
          [apiKey],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getMeilisearchApiKey() => (super.noSuchMethod(
        Invocation.method(
          #getMeilisearchApiKey,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> setNotificationServiceEnabled(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setNotificationServiceEnabled,
          [enabled],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> getNotificationServiceEnabled() => (super.noSuchMethod(
        Invocation.method(
          #getNotificationServiceEnabled,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> setNotificationWebhookUrl(String? url) =>
      (super.noSuchMethod(
        Invocation.method(
          #setNotificationWebhookUrl,
          [url],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getNotificationWebhookUrl() => (super.noSuchMethod(
        Invocation.method(
          #getNotificationWebhookUrl,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> setNotificationRetentionDays(int? days) =>
      (super.noSuchMethod(
        Invocation.method(
          #setNotificationRetentionDays,
          [days],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<int> getNotificationRetentionDays() => (super.noSuchMethod(
        Invocation.method(
          #getNotificationRetentionDays,
          [],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<void> setDataObservabilityEnabled(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setDataObservabilityEnabled,
          [enabled],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> getDataObservabilityEnabled() => (super.noSuchMethod(
        Invocation.method(
          #getDataObservabilityEnabled,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> setHealthCheckInterval(int? minutes) => (super.noSuchMethod(
        Invocation.method(
          #setHealthCheckInterval,
          [minutes],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<int> getHealthCheckInterval() => (super.noSuchMethod(
        Invocation.method(
          #getHealthCheckInterval,
          [],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<void> setMeilisearchIndexSyncInterval(int? hours) =>
      (super.noSuchMethod(
        Invocation.method(
          #setMeilisearchIndexSyncInterval,
          [hours],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<int> getMeilisearchIndexSyncInterval() => (super.noSuchMethod(
        Invocation.method(
          #getMeilisearchIndexSyncInterval,
          [],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);
}
