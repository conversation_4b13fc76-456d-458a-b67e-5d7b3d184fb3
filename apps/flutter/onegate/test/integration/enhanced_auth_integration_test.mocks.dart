// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in flutter_onegate/test/integration/enhanced_auth_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:io' as _i4;

import 'package:dio/dio.dart' as _i12;
import 'package:flutter/material.dart' as _i7;
import 'package:flutter_onegate/config/gate_config.dart' as _i3;
import 'package:flutter_onegate/data/datasources/gate_storage.dart' as _i2;
import 'package:flutter_onegate/data/datasources/remote_datasource.dart' as _i6;
import 'package:flutter_onegate/data/models/staff_model.dart' as _i13;
import 'package:flutter_onegate/domain/entities/visitor/purpose/purpose.dart'
    as _i9;
import 'package:flutter_onegate/domain/entities/visitor/visitor.dart' as _i8;
import 'package:flutter_onegate/domain/entities/visitor/visitorLog.dart'
    as _i10;
import 'package:flutter_onegate/presentation/features/visitor_checkin_flow/data/visitor_info.dart'
    as _i11;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGateStorage_0 extends _i1.SmartFake implements _i2.GateStorage {
  _FakeGateStorage_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeGateConfig_1 extends _i1.SmartFake implements _i3.GateConfig {
  _FakeGateConfig_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFile_2 extends _i1.SmartFake implements _i4.File {
  _FakeFile_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [GateStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockGateStorage extends _i1.Mock implements _i2.GateStorage {
  MockGateStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<void> init() => (super.noSuchMethod(
        Invocation.method(
          #init,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveFaceRecConfig(Map<String, dynamic>? config) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveFaceRecConfig,
          [config],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>?> getFaceRecConfig() => (super.noSuchMethod(
        Invocation.method(
          #getFaceRecConfig,
          [],
        ),
        returnValue: _i5.Future<Map<String, dynamic>?>.value(),
      ) as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<void> saveAccessToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #saveAccessToken,
          [token],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveRefreshToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #saveRefreshToken,
          [token],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveTokenExpiry(DateTime? expiryTime) => (super.noSuchMethod(
        Invocation.method(
          #saveTokenExpiry,
          [expiryTime],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveVisitorImageBase64(_i4.File? imageFile) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveVisitorImageBase64,
          [imageFile],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> removeVisitorImage() => (super.noSuchMethod(
        Invocation.method(
          #removeVisitorImage,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i4.File?> getVisitorImageBase64() => (super.noSuchMethod(
        Invocation.method(
          #getVisitorImageBase64,
          [],
        ),
        returnValue: _i5.Future<_i4.File?>.value(),
      ) as _i5.Future<_i4.File?>);

  @override
  _i5.Future<void> saveSocietyDetails(
    String? societyId,
    String? societyName,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveSocietyDetails,
          [
            societyId,
            societyName,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveSocietyId(String? societyId) => (super.noSuchMethod(
        Invocation.method(
          #saveSocietyId,
          [societyId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getSocietyId() => (super.noSuchMethod(
        Invocation.method(
          #getSocietyId,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<String?> getAccessToken() => (super.noSuchMethod(
        Invocation.method(
          #getAccessToken,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<String?> getRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #getRefreshToken,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<bool> isTokenExpired() => (super.noSuchMethod(
        Invocation.method(
          #isTokenExpired,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> saveUserId(String? userId) => (super.noSuchMethod(
        Invocation.method(
          #saveUserId,
          [userId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getUserId() => (super.noSuchMethod(
        Invocation.method(
          #getUserId,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveUsername(String? username) => (super.noSuchMethod(
        Invocation.method(
          #saveUsername,
          [username],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveImage(String? image) => (super.noSuchMethod(
        Invocation.method(
          #saveImage,
          [image],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setComingFrom(String? comingFrom) => (super.noSuchMethod(
        Invocation.method(
          #setComingFrom,
          [comingFrom],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getComingFrom() => (super.noSuchMethod(
        Invocation.method(
          #getComingFrom,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> removeComingFrom() => (super.noSuchMethod(
        Invocation.method(
          #removeComingFrom,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getImage() => (super.noSuchMethod(
        Invocation.method(
          #getImage,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<String?> getUsername() => (super.noSuchMethod(
        Invocation.method(
          #getUsername,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveUserEmail(String? email) => (super.noSuchMethod(
        Invocation.method(
          #saveUserEmail,
          [email],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getUserEmail() => (super.noSuchMethod(
        Invocation.method(
          #getUserEmail,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveUserFullName(String? fullName) => (super.noSuchMethod(
        Invocation.method(
          #saveUserFullName,
          [fullName],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getUserFullName() => (super.noSuchMethod(
        Invocation.method(
          #getUserFullName,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveUserRoles(List<String>? roles) => (super.noSuchMethod(
        Invocation.method(
          #saveUserRoles,
          [roles],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<String>> getUserRoles() => (super.noSuchMethod(
        Invocation.method(
          #getUserRoles,
          [],
        ),
        returnValue: _i5.Future<List<String>>.value(<String>[]),
      ) as _i5.Future<List<String>>);

  @override
  _i5.Future<void> saveSessionTimestamp(DateTime? timestamp) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveSessionTimestamp,
          [timestamp],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<DateTime?> getSessionTimestamp() => (super.noSuchMethod(
        Invocation.method(
          #getSessionTimestamp,
          [],
        ),
        returnValue: _i5.Future<DateTime?>.value(),
      ) as _i5.Future<DateTime?>);

  @override
  _i5.Future<void> saveRole(String? role) => (super.noSuchMethod(
        Invocation.method(
          #saveRole,
          [role],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<dynamic, dynamic>> getSocietyDetails() => (super.noSuchMethod(
        Invocation.method(
          #getSocietyDetails,
          [],
        ),
        returnValue:
            _i5.Future<Map<dynamic, dynamic>>.value(<dynamic, dynamic>{}),
      ) as _i5.Future<Map<dynamic, dynamic>>);

  @override
  _i5.Future<String?> getRole() => (super.noSuchMethod(
        Invocation.method(
          #getRole,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> saveMemberDetails(Map<String, dynamic>? memberDetails) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveMemberDetails,
          [memberDetails],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>?> getMemberDetails() => (super.noSuchMethod(
        Invocation.method(
          #getMemberDetails,
          [],
        ),
        returnValue: _i5.Future<Map<String, dynamic>?>.value(),
      ) as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<void> clearStorage(String? key) => (super.noSuchMethod(
        Invocation.method(
          #clearStorage,
          [key],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearTokens() => (super.noSuchMethod(
        Invocation.method(
          #clearTokens,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveVisitorLogId(String? visitorLogId) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveVisitorLogId,
          [visitorLogId],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getVisitorLogId() => (super.noSuchMethod(
        Invocation.method(
          #getVisitorLogId,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> clearVisitorLogId() => (super.noSuchMethod(
        Invocation.method(
          #clearVisitorLogId,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> clearVisitorImage() => (super.noSuchMethod(
        Invocation.method(
          #clearVisitorImage,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  bool? getTooglevalue(String? key) => (super.noSuchMethod(Invocation.method(
        #getTooglevalue,
        [key],
      )) as bool?);

  @override
  _i5.Future<void> setToogleValue(
    String? key,
    bool? value,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setToogleValue,
          [
            key,
            value,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> saveMemberList(List<dynamic>? memberList) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveMemberList,
          [memberList],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<dynamic>?> getMemberList() => (super.noSuchMethod(
        Invocation.method(
          #getMemberList,
          [],
        ),
        returnValue: _i5.Future<List<dynamic>?>.value(),
      ) as _i5.Future<List<dynamic>?>);

  @override
  _i5.Future<void> saveMemberApproval(bool? approval) => (super.noSuchMethod(
        Invocation.method(
          #saveMemberApproval,
          [approval],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool?> getMemberApproval() => (super.noSuchMethod(
        Invocation.method(
          #getMemberApproval,
          [],
        ),
        returnValue: _i5.Future<bool?>.value(),
      ) as _i5.Future<bool?>);

  @override
  _i5.Future<void> saveSelectedGate(
    String? gateName,
    String? gateId,
    String? gateType,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveSelectedGate,
          [
            gateName,
            gateId,
            gateType,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, String?>> getSelectedGate() => (super.noSuchMethod(
        Invocation.method(
          #getSelectedGate,
          [],
        ),
        returnValue:
            _i5.Future<Map<String, String?>>.value(<String, String?>{}),
      ) as _i5.Future<Map<String, String?>>);

  @override
  _i5.Future<String?> getGateType() => (super.noSuchMethod(
        Invocation.method(
          #getGateType,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> setMeilisearchHost(String? host) => (super.noSuchMethod(
        Invocation.method(
          #setMeilisearchHost,
          [host],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getMeilisearchHost() => (super.noSuchMethod(
        Invocation.method(
          #getMeilisearchHost,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> setMeilisearchApiKey(String? apiKey) => (super.noSuchMethod(
        Invocation.method(
          #setMeilisearchApiKey,
          [apiKey],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getMeilisearchApiKey() => (super.noSuchMethod(
        Invocation.method(
          #getMeilisearchApiKey,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> setNotificationServiceEnabled(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setNotificationServiceEnabled,
          [enabled],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> getNotificationServiceEnabled() => (super.noSuchMethod(
        Invocation.method(
          #getNotificationServiceEnabled,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> setNotificationWebhookUrl(String? url) =>
      (super.noSuchMethod(
        Invocation.method(
          #setNotificationWebhookUrl,
          [url],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String?> getNotificationWebhookUrl() => (super.noSuchMethod(
        Invocation.method(
          #getNotificationWebhookUrl,
          [],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<void> setNotificationRetentionDays(int? days) =>
      (super.noSuchMethod(
        Invocation.method(
          #setNotificationRetentionDays,
          [days],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<int> getNotificationRetentionDays() => (super.noSuchMethod(
        Invocation.method(
          #getNotificationRetentionDays,
          [],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<void> setDataObservabilityEnabled(bool? enabled) =>
      (super.noSuchMethod(
        Invocation.method(
          #setDataObservabilityEnabled,
          [enabled],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<bool> getDataObservabilityEnabled() => (super.noSuchMethod(
        Invocation.method(
          #getDataObservabilityEnabled,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<void> setHealthCheckInterval(int? minutes) => (super.noSuchMethod(
        Invocation.method(
          #setHealthCheckInterval,
          [minutes],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<int> getHealthCheckInterval() => (super.noSuchMethod(
        Invocation.method(
          #getHealthCheckInterval,
          [],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);

  @override
  _i5.Future<void> setMeilisearchIndexSyncInterval(int? hours) =>
      (super.noSuchMethod(
        Invocation.method(
          #setMeilisearchIndexSyncInterval,
          [hours],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<int> getMeilisearchIndexSyncInterval() => (super.noSuchMethod(
        Invocation.method(
          #getMeilisearchIndexSyncInterval,
          [],
        ),
        returnValue: _i5.Future<int>.value(0),
      ) as _i5.Future<int>);
}

/// A class which mocks [RemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockRemoteDataSource extends _i1.Mock implements _i6.RemoteDataSource {
  MockRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.GateStorage get gateStorage => (super.noSuchMethod(
        Invocation.getter(#gateStorage),
        returnValue: _FakeGateStorage_0(
          this,
          Invocation.getter(#gateStorage),
        ),
      ) as _i2.GateStorage);

  @override
  _i5.Future<Map<String, dynamic>> loginUser() => (super.noSuchMethod(
        Invocation.method(
          #loginUser,
          [],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<void> callMember(
    String? mobile,
    _i7.BuildContext? context, {
    String? name,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #callMember,
          [
            mobile,
            context,
          ],
          {#name: name},
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<dynamic>> getCallHistory(String? fromNumber) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCallHistory,
          [fromNumber],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<List<dynamic>> fetchGates() => (super.noSuchMethod(
        Invocation.method(
          #fetchGates,
          [],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<List<dynamic>> fetchSocieties(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchSocieties,
          [userId],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<void> sendFcmNotification(Map<String, dynamic>? requestData) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendFcmNotification,
          [requestData],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i3.GateConfig> fetchGateBaseDomain() => (super.noSuchMethod(
        Invocation.method(
          #fetchGateBaseDomain,
          [],
        ),
        returnValue: _i5.Future<_i3.GateConfig>.value(_FakeGateConfig_1(
          this,
          Invocation.method(
            #fetchGateBaseDomain,
            [],
          ),
        )),
      ) as _i5.Future<_i3.GateConfig>);

  @override
  _i5.Future<_i8.Visitor?> searchVisitor(String? mobileNumber) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchVisitor,
          [mobileNumber],
        ),
        returnValue: _i5.Future<_i8.Visitor?>.value(),
      ) as _i5.Future<_i8.Visitor?>);

  @override
  _i5.Future<List<_i9.PurposeCategory1>?> fetchPurpose() => (super.noSuchMethod(
        Invocation.method(
          #fetchPurpose,
          [],
        ),
        returnValue: _i5.Future<List<_i9.PurposeCategory1>?>.value(),
      ) as _i5.Future<List<_i9.PurposeCategory1>?>);

  @override
  _i5.Future<void> fetchAndStoreFaceRecConfig() => (super.noSuchMethod(
        Invocation.method(
          #fetchAndStoreFaceRecConfig,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i8.Visitor?> createVisitor(_i8.Visitor? visitor) =>
      (super.noSuchMethod(
        Invocation.method(
          #createVisitor,
          [visitor],
        ),
        returnValue: _i5.Future<_i8.Visitor?>.value(),
      ) as _i5.Future<_i8.Visitor?>);

  @override
  _i5.Future<List<_i10.VisitorLog>?> fetchCardNumbers() => (super.noSuchMethod(
        Invocation.method(
          #fetchCardNumbers,
          [],
        ),
        returnValue: _i5.Future<List<_i10.VisitorLog>?>.value(),
      ) as _i5.Future<List<_i10.VisitorLog>?>);

  @override
  _i5.Future<_i10.VisitorLog?> checkIn(
    _i10.VisitorLog? visitorLog, [
    bool? statusallowed,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkIn,
          [
            visitorLog,
            statusallowed,
          ],
        ),
        returnValue: _i5.Future<_i10.VisitorLog?>.value(),
      ) as _i5.Future<_i10.VisitorLog?>);

  @override
  _i5.Future<void> exportLogs(Map<String, dynamic>? visitorData) =>
      (super.noSuchMethod(
        Invocation.method(
          #exportLogs,
          [visitorData],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<_i10.VisitorLog>> fetchCheckInLogs() => (super.noSuchMethod(
        Invocation.method(
          #fetchCheckInLogs,
          [],
        ),
        returnValue:
            _i5.Future<List<_i10.VisitorLog>>.value(<_i10.VisitorLog>[]),
      ) as _i5.Future<List<_i10.VisitorLog>>);

  @override
  _i5.Future<List<_i10.VisitorLog>> fetchAllLogs() => (super.noSuchMethod(
        Invocation.method(
          #fetchAllLogs,
          [],
        ),
        returnValue:
            _i5.Future<List<_i10.VisitorLog>>.value(<_i10.VisitorLog>[]),
      ) as _i5.Future<List<_i10.VisitorLog>>);

  @override
  _i5.Future<List<_i10.VisitorLog>> fetchCheckOutLogs() => (super.noSuchMethod(
        Invocation.method(
          #fetchCheckOutLogs,
          [],
        ),
        returnValue:
            _i5.Future<List<_i10.VisitorLog>>.value(<_i10.VisitorLog>[]),
      ) as _i5.Future<List<_i10.VisitorLog>>);

  @override
  DateTime? tryParseDate(String? dateStr) =>
      (super.noSuchMethod(Invocation.method(
        #tryParseDate,
        [dateStr],
      )) as DateTime?);

  @override
  _i5.Future<Map<String, dynamic>> verifyPasscode({
    required String? companyId,
    String? passcode,
    int? id,
    String? mobile,
    bool? isStaff,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyPasscode,
          [],
          {
            #companyId: companyId,
            #passcode: passcode,
            #id: id,
            #mobile: mobile,
            #isStaff: isStaff,
          },
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<List<dynamic>> getMember(int? companyId) => (super.noSuchMethod(
        Invocation.method(
          #getMember,
          [companyId],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<List<dynamic>> getSubCategoryId(int? companyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSubCategoryId,
          [companyId],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<List<dynamic>> getMemberUnit(
    int? companyId,
    int? buildingId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMemberUnit,
          [
            companyId,
            buildingId,
          ],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<List<Map<String, dynamic>>> getBuilding(int? companyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getBuilding,
          [companyId],
        ),
        returnValue: _i5.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i5.Future<List<Map<String, dynamic>>>);

  @override
  _i5.Future<List<dynamic>> getBuildingsList() => (super.noSuchMethod(
        Invocation.method(
          #getBuildingsList,
          [],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> sendOtpForSelfCheckIn(
          String? mobileNumber) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendOtpForSelfCheckIn,
          [mobileNumber],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> verifySelfCheckin({
    required String? mobileNumber,
    required String? otp,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifySelfCheckin,
          [],
          {
            #mobileNumber: mobileNumber,
            #otp: otp,
          },
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<String?> sendOTP(String? mobileNumber) => (super.noSuchMethod(
        Invocation.method(
          #sendOTP,
          [mobileNumber],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<String?> verifyOTP(
    String? mobileNumber,
    String? otp,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyOTP,
          [
            mobileNumber,
            otp,
          ],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<bool> checkOut(_i10.VisitorLog? visitorLog) => (super.noSuchMethod(
        Invocation.method(
          #checkOut,
          [visitorLog],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> uploadParcelImage({
    required int? visitorLogId,
    required String? imageUrl,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadParcelImage,
          [],
          {
            #visitorLogId: visitorLogId,
            #imageUrl: imageUrl,
          },
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<List<_i11.VisitorInfo>> fetchApprovals({
    String? logID,
    bool? isSecondary,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchApprovals,
          [],
          {
            #logID: logID,
            #isSecondary: isSecondary,
          },
        ),
        returnValue:
            _i5.Future<List<_i11.VisitorInfo>>.value(<_i11.VisitorInfo>[]),
      ) as _i5.Future<List<_i11.VisitorInfo>>);

  @override
  int parseToInt(dynamic value) => (super.noSuchMethod(
        Invocation.method(
          #parseToInt,
          [value],
        ),
        returnValue: 0,
      ) as int);

  @override
  _i5.Future<void> sendLogs(Map<String, dynamic>? visitorData) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendLogs,
          [visitorData],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i12.Response<dynamic>?> readStatus(
    String? memberID,
    String? visitorId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #readStatus,
          [
            memberID,
            visitorId,
          ],
        ),
        returnValue: _i5.Future<_i12.Response<dynamic>?>.value(),
      ) as _i5.Future<_i12.Response<dynamic>?>);

  @override
  _i5.Future<_i4.File> compressImage(_i4.File? file) => (super.noSuchMethod(
        Invocation.method(
          #compressImage,
          [file],
        ),
        returnValue: _i5.Future<_i4.File>.value(_FakeFile_2(
          this,
          Invocation.method(
            #compressImage,
            [file],
          ),
        )),
      ) as _i5.Future<_i4.File>);

  @override
  _i5.Future<String?> uploadFile(
    _i4.File? thisfile,
    String? userMobile,
    int? companyId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadFile,
          [
            thisfile,
            userMobile,
            companyId,
          ],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);

  @override
  _i5.Future<Map<String, dynamic>> getMembersList({String? buildingName}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMembersList,
          [],
          {#buildingName: buildingName},
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<List<dynamic>?> getCachedData() => (super.noSuchMethod(
        Invocation.method(
          #getCachedData,
          [],
        ),
        returnValue: _i5.Future<List<dynamic>?>.value(),
      ) as _i5.Future<List<dynamic>?>);

  @override
  _i5.Future<void> cacheData(List<dynamic>? data) => (super.noSuchMethod(
        Invocation.method(
          #cacheData,
          [data],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> cacheMeta(Map<String, dynamic>? meta) => (super.noSuchMethod(
        Invocation.method(
          #cacheMeta,
          [meta],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<Map<String, dynamic>?> getCachedMeta() => (super.noSuchMethod(
        Invocation.method(
          #getCachedMeta,
          [],
        ),
        returnValue: _i5.Future<Map<String, dynamic>?>.value(),
      ) as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<List<dynamic>> getUnitsList(int? buildingId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUnitsList,
          [buildingId],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>?> fetchStaffById(int? staffId) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchStaffById,
          [staffId],
        ),
        returnValue: _i5.Future<Map<String, dynamic>?>.value(),
      ) as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<List<_i13.StaffModel>> fetchStaffList(String? companyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchStaffList,
          [companyId],
        ),
        returnValue:
            _i5.Future<List<_i13.StaffModel>>.value(<_i13.StaffModel>[]),
      ) as _i5.Future<List<_i13.StaffModel>>);

  @override
  _i5.Future<void> makeExotelCall({
    required String? memberMobileNumber,
    required int? visitorId,
    required int? memberId,
    required int? visitorLogId,
    required String? purposeCategory,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #makeExotelCall,
          [],
          {
            #memberMobileNumber: memberMobileNumber,
            #visitorId: visitorId,
            #memberId: memberId,
            #visitorLogId: visitorLogId,
            #purposeCategory: purposeCategory,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<List<dynamic>> fetchParcels() => (super.noSuchMethod(
        Invocation.method(
          #fetchParcels,
          [],
        ),
        returnValue: _i5.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i5.Future<List<dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> verifyParcelOtp(
    String? parcelId,
    String? otp,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyParcelOtp,
          [
            parcelId,
            otp,
          ],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<Map<String, dynamic>> getParcelOtp(
    String? parcelId,
    String? mobileNumber,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getParcelOtp,
          [
            parcelId,
            mobileNumber,
          ],
        ),
        returnValue:
            _i5.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i5.Future<Map<String, dynamic>>);

  @override
  _i5.Future<dynamic> fetchStaffCategory() => (super.noSuchMethod(
        Invocation.method(
          #fetchStaffCategory,
          [],
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  _i5.Future<dynamic> addStaff(Map<String, dynamic>? staffData) =>
      (super.noSuchMethod(
        Invocation.method(
          #addStaff,
          [staffData],
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  _i5.Future<dynamic> editStaff(
    int? staffId,
    Map<String, dynamic>? staffData,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #editStaff,
          [
            staffId,
            staffData,
          ],
        ),
        returnValue: _i5.Future<dynamic>.value(),
      ) as _i5.Future<dynamic>);

  @override
  _i5.Future<Map<String, dynamic>?> uploadStaffImages(
    _i4.File? file,
    int? companyId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadStaffImages,
          [
            file,
            companyId,
          ],
        ),
        returnValue: _i5.Future<Map<String, dynamic>?>.value(),
      ) as _i5.Future<Map<String, dynamic>?>);

  @override
  _i5.Future<bool> updateVisitor(_i8.Visitor? visitor) => (super.noSuchMethod(
        Invocation.method(
          #updateVisitor,
          [visitor],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);
}
