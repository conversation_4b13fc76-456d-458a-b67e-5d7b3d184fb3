import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_onegate/presentation/widgets/session_expired_bottom_sheet.dart';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';
import 'package:flutter_onegate/services/auth_service/enhanced_token_refresh_manager.dart';

// Generate mocks
@GenerateMocks([AuthService, EnhancedTokenRefreshManager])
import 'session_expired_bottom_sheet_test.mocks.dart';

void main() {
  group('SessionExpiredBottomSheet Tests', () {
    late MockAuthService mockAuthService;
    late MockEnhancedTokenRefreshManager mockTokenManager;

    setUpAll(() {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      // Reset GetIt instance
      GetIt.instance.reset();

      // Create mocks
      mockAuthService = MockAuthService();
      mockTokenManager = MockEnhancedTokenRefreshManager();

      // Register mocks
      GetIt.instance.registerSingleton<AuthService>(mockAuthService);

      // Setup mock behavior
      when(mockAuthService.tokenRefreshManager).thenReturn(mockTokenManager);
      when(mockTokenManager.clearTokens()).thenAnswer((_) async {});
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    group('Widget Rendering', () {
      testWidgets('should render session expired bottom sheet correctly', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Verify main elements are present
        expect(find.text('Session Expired'), findsOneWidget);
        expect(find.text('Your session has expired for security reasons. Please log in again to continue.'), findsOneWidget);
        expect(find.text('Login Again'), findsOneWidget);
        expect(find.byIcon(Icons.lock_clock), findsOneWidget);
      });

      testWidgets('should render custom error message when provided', (WidgetTester tester) async {
        const customMessage = 'Custom session expiration message';
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(
                errorMessage: customMessage,
              ),
            ),
          ),
        );

        expect(find.text(customMessage), findsOneWidget);
      });

      testWidgets('should show loading state when logging out', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Tap the login button
        await tester.tap(find.text('Login Again'));
        await tester.pump();

        // Should show loading state
        expect(find.text('Logging out...'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });
    });

    group('Animations', () {
      testWidgets('should start slide animation on init', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Initial state - should be off screen
        await tester.pump(Duration.zero);
        
        // After animation starts
        await tester.pump(const Duration(milliseconds: 150));
        
        // Should be animating in
        expect(find.byType(SessionExpiredBottomSheet), findsOneWidget);
      });

      testWidgets('should animate icon rotation and pulse', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Let animations run
        await tester.pump();
        await tester.pump(const Duration(milliseconds: 500));
        await tester.pump(const Duration(milliseconds: 1000));

        // Icon should be present and animating
        expect(find.byIcon(Icons.lock_clock), findsOneWidget);
      });
    });

    group('User Interactions', () {
      testWidgets('should handle login button tap', (WidgetTester tester) async {
        bool loginCompleted = false;
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SessionExpiredBottomSheet(
                onLoginComplete: () {
                  loginCompleted = true;
                },
              ),
            ),
          ),
        );

        // Tap login button
        await tester.tap(find.text('Login Again'));
        await tester.pump();

        // Should show loading state
        expect(find.text('Logging out...'), findsOneWidget);
        
        // Verify token clearing was called
        verify(mockTokenManager.clearTokens()).called(1);
      });

      testWidgets('should prevent multiple login attempts', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Tap login button multiple times
        await tester.tap(find.text('Login Again'));
        await tester.pump();
        
        // Button should be disabled now
        final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
        expect(button.onPressed, isNull);
      });

      testWidgets('should not be dismissible by back button', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Try to pop with back button
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/navigation',
          null,
          (data) {},
        );

        await tester.pump();

        // Should still be present
        expect(find.byType(SessionExpiredBottomSheet), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle token clearing errors gracefully', (WidgetTester tester) async {
        // Setup mock to throw error
        when(mockTokenManager.clearTokens()).thenThrow(Exception('Token clearing failed'));

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Tap login button
        await tester.tap(find.text('Login Again'));
        await tester.pump();

        // Should handle error gracefully
        expect(find.byType(SessionExpiredBottomSheet), findsOneWidget);
      });
    });

    group('Styling and Appearance', () {
      testWidgets('should have correct styling', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Check container styling
        final container = tester.widget<Container>(
          find.descendant(
            of: find.byType(SessionExpiredBottomSheet),
            matching: find.byType(Container),
          ).first,
        );

        expect(container.decoration, isA<BoxDecoration>());
        final decoration = container.decoration as BoxDecoration;
        expect(decoration.color, equals(Colors.white));
        expect(decoration.borderRadius, isNotNull);
      });

      testWidgets('should have correct button styling', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
        expect(button.style, isNotNull);
      });
    });

    group('Integration', () {
      testWidgets('should integrate with authentication services', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: const SessionExpiredBottomSheet(),
            ),
          ),
        );

        // Tap login button
        await tester.tap(find.text('Login Again'));
        await tester.pump();

        // Verify integration with auth services
        verify(mockAuthService.tokenRefreshManager).called(greaterThan(0));
        verify(mockTokenManager.clearTokens()).called(1);
      });
    });
  });

  group('showSessionExpiredBottomSheet Function', () {
    testWidgets('should show bottom sheet when called', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => showSessionExpiredBottomSheet(),
                  child: const Text('Show Bottom Sheet'),
                );
              },
            ),
          ),
        ),
      );

      // Tap button to show bottom sheet
      await tester.tap(find.text('Show Bottom Sheet'));
      await tester.pumpAndSettle();

      // Should show the bottom sheet
      expect(find.byType(SessionExpiredBottomSheet), findsOneWidget);
    });

    testWidgets('should pass custom error message', (WidgetTester tester) async {
      const customMessage = 'Custom error from function';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () => showSessionExpiredBottomSheet(
                    errorMessage: customMessage,
                  ),
                  child: const Text('Show Bottom Sheet'),
                );
              },
            ),
          ),
        ),
      );

      // Tap button to show bottom sheet
      await tester.tap(find.text('Show Bottom Sheet'));
      await tester.pumpAndSettle();

      // Should show custom message
      expect(find.text(customMessage), findsOneWidget);
    });
  });
}
