// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in flutter_onegate/test/presentation/widgets/session_expired_bottom_sheet_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;

import 'package:flutter_onegate/data/datasources/gate_storage.dart' as _i2;
import 'package:flutter_onegate/data/datasources/remote_datasource.dart' as _i3;
import 'package:flutter_onegate/services/auth_service/auth_service.dart' as _i5;
import 'package:flutter_onegate/services/auth_service/enhanced_token_refresh_manager.dart'
    as _i4;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGateStorage_0 extends _i1.SmartFake implements _i2.GateStorage {
  _FakeGateStorage_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRemoteDataSource_1 extends _i1.SmartFake
    implements _i3.RemoteDataSource {
  _FakeRemoteDataSource_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeEnhancedTokenRefreshManager_2 extends _i1.SmartFake
    implements _i4.EnhancedTokenRefreshManager {
  _FakeEnhancedTokenRefreshManager_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i5.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.GateStorage get gateStorage => (super.noSuchMethod(
        Invocation.getter(#gateStorage),
        returnValue: _FakeGateStorage_0(
          this,
          Invocation.getter(#gateStorage),
        ),
      ) as _i2.GateStorage);

  @override
  _i3.RemoteDataSource get remoteDataSource => (super.noSuchMethod(
        Invocation.getter(#remoteDataSource),
        returnValue: _FakeRemoteDataSource_1(
          this,
          Invocation.getter(#remoteDataSource),
        ),
      ) as _i3.RemoteDataSource);

  @override
  _i4.EnhancedTokenRefreshManager get tokenRefreshManager =>
      (super.noSuchMethod(
        Invocation.getter(#tokenRefreshManager),
        returnValue: _FakeEnhancedTokenRefreshManager_2(
          this,
          Invocation.getter(#tokenRefreshManager),
        ),
      ) as _i4.EnhancedTokenRefreshManager);

  @override
  _i6.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<Map<String, dynamic>?> login() => (super.noSuchMethod(
        Invocation.method(
          #login,
          [],
        ),
        returnValue: _i6.Future<Map<String, dynamic>?>.value(),
      ) as _i6.Future<Map<String, dynamic>?>);

  @override
  _i6.Future<List<dynamic>> fetchSocieties(String? userId) =>
      (super.noSuchMethod(
        Invocation.method(
          #fetchSocieties,
          [userId],
        ),
        returnValue: _i6.Future<List<dynamic>>.value(<dynamic>[]),
      ) as _i6.Future<List<dynamic>>);

  @override
  _i6.Future<bool> refreshToken() => (super.noSuchMethod(
        Invocation.method(
          #refreshToken,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<String?> getValidAccessToken() => (super.noSuchMethod(
        Invocation.method(
          #getValidAccessToken,
          [],
        ),
        returnValue: _i6.Future<String?>.value(),
      ) as _i6.Future<String?>);

  @override
  _i6.Future<void> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> isAuthenticated() => (super.noSuchMethod(
        Invocation.method(
          #isAuthenticated,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<Map<String, dynamic>?> getCurrentUserSession() =>
      (super.noSuchMethod(
        Invocation.method(
          #getCurrentUserSession,
          [],
        ),
        returnValue: _i6.Future<Map<String, dynamic>?>.value(),
      ) as _i6.Future<Map<String, dynamic>?>);
}

/// A class which mocks [EnhancedTokenRefreshManager].
///
/// See the documentation for Mockito's code generation for more information.
class MockEnhancedTokenRefreshManager extends _i1.Mock
    implements _i4.EnhancedTokenRefreshManager {
  MockEnhancedTokenRefreshManager() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<void> initialize(_i2.GateStorage? gateStorage) =>
      (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [gateStorage],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  void stopPeriodicRefreshCheck() => super.noSuchMethod(
        Invocation.method(
          #stopPeriodicRefreshCheck,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i6.Future<String?> getValidAccessToken() => (super.noSuchMethod(
        Invocation.method(
          #getValidAccessToken,
          [],
        ),
        returnValue: _i6.Future<String?>.value(),
      ) as _i6.Future<String?>);

  @override
  _i6.Future<bool> refreshTokenIfNeeded() => (super.noSuchMethod(
        Invocation.method(
          #refreshTokenIfNeeded,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<bool> isRefreshTokenValid() => (super.noSuchMethod(
        Invocation.method(
          #isRefreshTokenValid,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<String?> getRefreshToken() => (super.noSuchMethod(
        Invocation.method(
          #getRefreshToken,
          [],
        ),
        returnValue: _i6.Future<String?>.value(),
      ) as _i6.Future<String?>);

  @override
  _i6.Future<void> showCurrentTokenInfo() => (super.noSuchMethod(
        Invocation.method(
          #showCurrentTokenInfo,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> clearTokens() => (super.noSuchMethod(
        Invocation.method(
          #clearTokens,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
