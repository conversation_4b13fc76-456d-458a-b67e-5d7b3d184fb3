class StaffEntity {
  final int id;
  final String name;
  final String category;
  final String staffBadgeNumber;
  final String staffContactNumber;
  final String staffDob;
  final String staffQualification;
  final String staffSkill;
  final String languageSpoken;
  final String status;

  StaffEntity({
    required this.id,
    required this.name,
    required this.category,
    required this.staffBadgeNumber,
    required this.staffContactNumber,
    required this.staffDob,
    required this.staffQualification,
    required this.staffSkill,
    required this.languageSpoken,
    required this.status,
  });
}
