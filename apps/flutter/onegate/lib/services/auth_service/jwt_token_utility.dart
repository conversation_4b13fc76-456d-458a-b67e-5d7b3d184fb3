import 'dart:convert';
import 'dart:developer';

/// Utility class for JWT token operations
class JwtTokenUtility {
  /// Parse JWT token and extract claims
  static Map<String, dynamic>? parseJwtToken(String token) {
    try {
      // JWT tokens have 3 parts separated by dots: header.payload.signature
      final parts = token.split('.');
      if (parts.length != 3) {
        log("❌ Invalid JWT format");
        return null;
      }

      // Decode the payload (second part)
      final payload = parts[1];
      // Add padding if needed for base64 decoding
      final normalizedPayload = base64Url.normalize(payload);
      final decodedBytes = base64Url.decode(normalizedPayload);
      final decodedPayload = utf8.decode(decodedBytes);
      final payloadJson = jsonDecode(decodedPayload) as Map<String, dynamic>;

      return payloadJson;
    } catch (e) {
      log("❌ Error parsing JWT token: $e");
      return null;
    }
  }

  /// Extract expiration time from JWT token
  static DateTime? getTokenExpirationTime(String token) {
    try {
      final payload = parseJwtToken(token);
      if (payload == null) return null;

      final exp = payload['exp'];
      if (exp == null) return null;

      // JWT exp claim is in seconds since epoch
      return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    } catch (e) {
      log("❌ Error extracting token expiration: $e");
      return null;
    }
  }

  /// Check if token is expired or will expire within the given buffer time
  static bool isTokenExpiredOrExpiring(String token, {Duration buffer = const Duration(minutes: 5)}) {
    try {
      final expirationTime = getTokenExpirationTime(token);
      if (expirationTime == null) {
        log("⚠️ Could not determine token expiration, considering it expired");
        return true;
      }

      final now = DateTime.now();
      final expirationWithBuffer = expirationTime.subtract(buffer);
      
      final isExpiring = now.isAfter(expirationWithBuffer);
      
      if (isExpiring) {
        log("🔄 Token will expire at $expirationTime (in ${expirationTime.difference(now).inMinutes} minutes)");
      }
      
      return isExpiring;
    } catch (e) {
      log("❌ Error checking token expiration: $e");
      return true; // Consider expired on error
    }
  }

  /// Extract user information from JWT token
  static Map<String, dynamic>? getUserInfoFromToken(String token) {
    try {
      final payload = parseJwtToken(token);
      if (payload == null) return null;

      return {
        'sub': payload['sub'],
        'email': payload['email'],
        'preferred_username': payload['preferred_username'],
        'name': payload['name'],
        'roles': payload['realm_access']?['roles'] ?? [],
        'client_id': payload['azp'] ?? payload['aud'],
        'issuer': payload['iss'],
        'issued_at': payload['iat'] != null 
            ? DateTime.fromMillisecondsSinceEpoch(payload['iat'] * 1000) 
            : null,
        'expires_at': payload['exp'] != null 
            ? DateTime.fromMillisecondsSinceEpoch(payload['exp'] * 1000) 
            : null,
        'scopes': payload['scope']?.split(' ') ?? [],
      };
    } catch (e) {
      log("❌ Error extracting user info from token: $e");
      return null;
    }
  }

  /// Validate JWT token structure and basic claims
  static bool isValidJwtToken(String token) {
    try {
      final payload = parseJwtToken(token);
      if (payload == null) return false;

      // Check required claims
      final hasRequiredClaims = payload.containsKey('sub') && 
                               payload.containsKey('exp') && 
                               payload.containsKey('iat');

      if (!hasRequiredClaims) {
        log("❌ JWT token missing required claims");
        return false;
      }

      // Check if token is not expired
      final exp = payload['exp'];
      if (exp != null) {
        final expirationTime = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
        if (DateTime.now().isAfter(expirationTime)) {
          log("❌ JWT token is expired");
          return false;
        }
      }

      return true;
    } catch (e) {
      log("❌ Error validating JWT token: $e");
      return false;
    }
  }

  /// Get time until token expiration
  static Duration? getTimeUntilExpiration(String token) {
    try {
      final expirationTime = getTokenExpirationTime(token);
      if (expirationTime == null) return null;

      final now = DateTime.now();
      if (now.isAfter(expirationTime)) {
        return Duration.zero; // Already expired
      }

      return expirationTime.difference(now);
    } catch (e) {
      log("❌ Error calculating time until expiration: $e");
      return null;
    }
  }

  /// Log detailed token information for debugging
  static void logTokenDetails(String tokenType, String token) {
    try {
      final userInfo = getUserInfoFromToken(token);
      if (userInfo == null) {
        log("❌ Could not extract user info from $tokenType");
        return;
      }

      log("📋 ===== $tokenType TOKEN DETAILS =====");
      log("👤 Subject: ${userInfo['sub']}");
      log("📧 Email: ${userInfo['email']}");
      log("👤 Username: ${userInfo['preferred_username']}");
      log("🏢 Name: ${userInfo['name']}");
      log("🔑 Client ID: ${userInfo['client_id']}");
      log("🏛️ Issuer: ${userInfo['issuer']}");
      log("⏰ Issued At: ${userInfo['issued_at']}");
      log("⏰ Expires At: ${userInfo['expires_at']}");
      log("🔐 Scopes: ${userInfo['scopes']}");
      log("🎭 Roles: ${userInfo['roles']}");
      
      final timeUntilExpiration = getTimeUntilExpiration(token);
      if (timeUntilExpiration != null) {
        log("⏱️ Time until expiration: ${timeUntilExpiration.inMinutes} minutes");
      }
      
      log("📋 =====================================");
    } catch (e) {
      log("❌ Error logging token details: $e");
    }
  }
}
