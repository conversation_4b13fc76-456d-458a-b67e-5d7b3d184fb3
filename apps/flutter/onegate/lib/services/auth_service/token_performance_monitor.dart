import 'dart:async';
import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_onegate/services/auth_service/optimized_token_refresh_manager.dart';
import 'package:flutter_onegate/services/auth_service/optimized_auth_interceptor.dart';

/// Performance monitoring service for token refresh operations
/// Tracks metrics, identifies bottlenecks, and provides optimization insights
class TokenPerformanceMonitor {
  static final TokenPerformanceMonitor _instance = TokenPerformanceMonitor._internal();
  factory TokenPerformanceMonitor() => _instance;
  TokenPerformanceMonitor._internal();

  // Performance tracking
  final List<TokenRefreshMetric> _refreshMetrics = [];
  final List<RequestMetric> _requestMetrics = [];
  Timer? _monitoringTimer;
  DateTime? _monitoringStartTime;
  
  // Performance thresholds
  static const Duration _targetRefreshTime = Duration(seconds: 2);
  static const Duration _warningRefreshTime = Duration(seconds: 3);
  static const Duration _criticalRefreshTime = Duration(seconds: 5);
  static const double _targetSuccessRate = 95.0;
  static const int _maxMetricsHistory = 100;

  // Storage keys
  static const String _metricsEnabledKey = 'token_performance_monitoring_enabled';
  static const String _alertsEnabledKey = 'token_performance_alerts_enabled';

  bool _monitoringEnabled = true;
  bool _alertsEnabled = true;

  /// Initialize the performance monitor
  Future<void> initialize() async {
    await _loadSettings();
    _monitoringStartTime = DateTime.now();
    _startPerformanceMonitoring();
    log("📊 TokenPerformanceMonitor initialized");
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _monitoringEnabled = prefs.getBool(_metricsEnabledKey) ?? true;
      _alertsEnabled = prefs.getBool(_alertsEnabledKey) ?? true;
    } catch (e) {
      log("⚠️ Error loading performance monitor settings: $e");
    }
  }

  /// Save settings to SharedPreferences
  Future<void> saveSettings({bool? monitoringEnabled, bool? alertsEnabled}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (monitoringEnabled != null) {
        await prefs.setBool(_metricsEnabledKey, monitoringEnabled);
        _monitoringEnabled = monitoringEnabled;
      }
      
      if (alertsEnabled != null) {
        await prefs.setBool(_alertsEnabledKey, alertsEnabled);
        _alertsEnabled = alertsEnabled;
      }
    } catch (e) {
      log("❌ Error saving performance monitor settings: $e");
    }
  }

  /// Start performance monitoring
  void _startPerformanceMonitoring() {
    if (!_monitoringEnabled) return;
    
    _monitoringTimer?.cancel();
    _monitoringTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _analyzePerformance();
      _cleanupOldMetrics();
    });
  }

  /// Record token refresh metric
  void recordTokenRefresh({
    required Duration duration,
    required bool success,
    String? errorMessage,
    String? refreshTrigger,
  }) {
    if (!_monitoringEnabled) return;

    final metric = TokenRefreshMetric(
      timestamp: DateTime.now(),
      duration: duration,
      success: success,
      errorMessage: errorMessage,
      refreshTrigger: refreshTrigger,
    );

    _refreshMetrics.add(metric);
    
    // Check for performance issues
    if (_alertsEnabled) {
      _checkRefreshPerformance(metric);
    }

    log("📊 Token refresh recorded: ${duration.inMilliseconds}ms, success: $success");
  }

  /// Record request metric
  void recordRequest({
    required String endpoint,
    required Duration duration,
    required bool tokenRefreshRequired,
    required bool success,
  }) {
    if (!_monitoringEnabled) return;

    final metric = RequestMetric(
      timestamp: DateTime.now(),
      endpoint: endpoint,
      duration: duration,
      tokenRefreshRequired: tokenRefreshRequired,
      success: success,
    );

    _requestMetrics.add(metric);
  }

  /// Check refresh performance and alert if needed
  void _checkRefreshPerformance(TokenRefreshMetric metric) {
    if (metric.duration >= _criticalRefreshTime) {
      _logPerformanceAlert(
        "🚨 CRITICAL: Token refresh took ${metric.duration.inMilliseconds}ms (target: ${_targetRefreshTime.inMilliseconds}ms)",
        AlertLevel.critical,
      );
    } else if (metric.duration >= _warningRefreshTime) {
      _logPerformanceAlert(
        "⚠️ WARNING: Token refresh took ${metric.duration.inMilliseconds}ms (target: ${_targetRefreshTime.inMilliseconds}ms)",
        AlertLevel.warning,
      );
    }

    if (!metric.success) {
      _logPerformanceAlert(
        "❌ ERROR: Token refresh failed - ${metric.errorMessage ?? 'Unknown error'}",
        AlertLevel.error,
      );
    }
  }

  /// Analyze overall performance
  void _analyzePerformance() {
    if (_refreshMetrics.isEmpty) return;

    final recentMetrics = _refreshMetrics.where(
      (m) => DateTime.now().difference(m.timestamp) <= const Duration(hours: 1)
    ).toList();

    if (recentMetrics.isEmpty) return;

    // Calculate success rate
    final successCount = recentMetrics.where((m) => m.success).length;
    final successRate = (successCount / recentMetrics.length) * 100;

    // Calculate average duration
    final totalDuration = recentMetrics.fold<int>(
      0, (sum, m) => sum + m.duration.inMilliseconds
    );
    final avgDuration = Duration(milliseconds: totalDuration ~/ recentMetrics.length);

    // Check for performance degradation
    if (successRate < _targetSuccessRate) {
      _logPerformanceAlert(
        "📉 Performance degradation detected: Success rate ${successRate.toStringAsFixed(1)}% (target: ${_targetSuccessRate}%)",
        AlertLevel.warning,
      );
    }

    if (avgDuration > _warningRefreshTime) {
      _logPerformanceAlert(
        "🐌 Performance degradation detected: Average refresh time ${avgDuration.inMilliseconds}ms (target: ${_targetRefreshTime.inMilliseconds}ms)",
        AlertLevel.warning,
      );
    }

    log("📊 Performance analysis: ${recentMetrics.length} refreshes, ${successRate.toStringAsFixed(1)}% success, ${avgDuration.inMilliseconds}ms avg");
  }

  /// Log performance alert
  void _logPerformanceAlert(String message, AlertLevel level) {
    switch (level) {
      case AlertLevel.info:
        log("ℹ️ $message");
        break;
      case AlertLevel.warning:
        log("⚠️ $message");
        break;
      case AlertLevel.error:
        log("❌ $message");
        break;
      case AlertLevel.critical:
        log("🚨 $message");
        if (kDebugMode) {
          debugPrint("CRITICAL PERFORMANCE ISSUE: $message");
        }
        break;
    }
  }

  /// Clean up old metrics to prevent memory issues
  void _cleanupOldMetrics() {
    final cutoffTime = DateTime.now().subtract(const Duration(hours: 24));
    
    _refreshMetrics.removeWhere((m) => m.timestamp.isBefore(cutoffTime));
    _requestMetrics.removeWhere((m) => m.timestamp.isBefore(cutoffTime));
    
    // Limit total metrics
    if (_refreshMetrics.length > _maxMetricsHistory) {
      _refreshMetrics.removeRange(0, _refreshMetrics.length - _maxMetricsHistory);
    }
    
    if (_requestMetrics.length > _maxMetricsHistory) {
      _requestMetrics.removeRange(0, _requestMetrics.length - _maxMetricsHistory);
    }
  }

  /// Get comprehensive performance report
  Map<String, dynamic> getPerformanceReport() {
    final now = DateTime.now();
    final last24Hours = _refreshMetrics.where(
      (m) => now.difference(m.timestamp) <= const Duration(hours: 24)
    ).toList();
    
    final lastHour = _refreshMetrics.where(
      (m) => now.difference(m.timestamp) <= const Duration(hours: 1)
    ).toList();

    // Calculate metrics
    final totalRefreshes = last24Hours.length;
    final successfulRefreshes = last24Hours.where((m) => m.success).length;
    final successRate = totalRefreshes > 0 ? (successfulRefreshes / totalRefreshes * 100) : 0;
    
    final durations = last24Hours.map((m) => m.duration.inMilliseconds).toList();
    final avgDuration = durations.isNotEmpty ? durations.reduce((a, b) => a + b) / durations.length : 0;
    final minDuration = durations.isNotEmpty ? durations.reduce((a, b) => a < b ? a : b) : 0;
    final maxDuration = durations.isNotEmpty ? durations.reduce((a, b) => a > b ? a : b) : 0;

    // Performance grade
    String performanceGrade = 'A';
    if (avgDuration > _criticalRefreshTime.inMilliseconds || successRate < 80) {
      performanceGrade = 'F';
    } else if (avgDuration > _warningRefreshTime.inMilliseconds || successRate < 90) {
      performanceGrade = 'C';
    } else if (avgDuration > _targetRefreshTime.inMilliseconds || successRate < 95) {
      performanceGrade = 'B';
    }

    return {
      'monitoringEnabled': _monitoringEnabled,
      'alertsEnabled': _alertsEnabled,
      'monitoringDuration': _monitoringStartTime != null 
          ? now.difference(_monitoringStartTime!).inHours 
          : 0,
      'performanceGrade': performanceGrade,
      'last24Hours': {
        'totalRefreshes': totalRefreshes,
        'successfulRefreshes': successfulRefreshes,
        'failedRefreshes': totalRefreshes - successfulRefreshes,
        'successRate': successRate,
        'averageDuration': avgDuration,
        'minDuration': minDuration,
        'maxDuration': maxDuration,
      },
      'lastHour': {
        'totalRefreshes': lastHour.length,
        'successRate': lastHour.isNotEmpty 
            ? (lastHour.where((m) => m.success).length / lastHour.length * 100) 
            : 0,
      },
      'thresholds': {
        'target': _targetRefreshTime.inMilliseconds,
        'warning': _warningRefreshTime.inMilliseconds,
        'critical': _criticalRefreshTime.inMilliseconds,
        'targetSuccessRate': _targetSuccessRate,
      },
      'recommendations': _getPerformanceRecommendations(avgDuration, successRate),
    };
  }

  /// Get performance recommendations
  List<String> _getPerformanceRecommendations(double avgDuration, double successRate) {
    final recommendations = <String>[];

    if (avgDuration > _criticalRefreshTime.inMilliseconds) {
      recommendations.add("Critical: Token refresh is extremely slow. Check network connectivity and server performance.");
    } else if (avgDuration > _warningRefreshTime.inMilliseconds) {
      recommendations.add("Warning: Token refresh is slower than optimal. Consider network optimization.");
    }

    if (successRate < 80) {
      recommendations.add("Critical: High failure rate detected. Check authentication configuration and network stability.");
    } else if (successRate < 95) {
      recommendations.add("Warning: Some token refreshes are failing. Monitor for intermittent issues.");
    }

    if (avgDuration <= _targetRefreshTime.inMilliseconds && successRate >= _targetSuccessRate) {
      recommendations.add("Excellent: Token refresh performance is optimal.");
    }

    return recommendations;
  }

  /// Export metrics for analysis
  Map<String, dynamic> exportMetrics() {
    return {
      'refreshMetrics': _refreshMetrics.map((m) => m.toMap()).toList(),
      'requestMetrics': _requestMetrics.map((m) => m.toMap()).toList(),
      'exportTimestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Clear all metrics
  void clearMetrics() {
    _refreshMetrics.clear();
    _requestMetrics.clear();
    log("🧹 Performance metrics cleared");
  }

  /// Dispose the monitor
  void dispose() {
    _monitoringTimer?.cancel();
    _refreshMetrics.clear();
    _requestMetrics.clear();
    log("🛑 TokenPerformanceMonitor disposed");
  }
}

/// Token refresh metric data class
class TokenRefreshMetric {
  final DateTime timestamp;
  final Duration duration;
  final bool success;
  final String? errorMessage;
  final String? refreshTrigger;

  TokenRefreshMetric({
    required this.timestamp,
    required this.duration,
    required this.success,
    this.errorMessage,
    this.refreshTrigger,
  });

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'duration': duration.inMilliseconds,
      'success': success,
      'errorMessage': errorMessage,
      'refreshTrigger': refreshTrigger,
    };
  }
}

/// Request metric data class
class RequestMetric {
  final DateTime timestamp;
  final String endpoint;
  final Duration duration;
  final bool tokenRefreshRequired;
  final bool success;

  RequestMetric({
    required this.timestamp,
    required this.endpoint,
    required this.duration,
    required this.tokenRefreshRequired,
    required this.success,
  });

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'endpoint': endpoint,
      'duration': duration.inMilliseconds,
      'tokenRefreshRequired': tokenRefreshRequired,
      'success': success,
    };
  }
}

/// Alert levels for performance monitoring
enum AlertLevel {
  info,
  warning,
  error,
  critical,
}
