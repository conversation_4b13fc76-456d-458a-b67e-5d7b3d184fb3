import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter_onegate/services/auth_service/optimized_token_refresh_manager.dart';
import 'package:flutter_onegate/services/auth_service/jwt_token_utility.dart';
import 'package:flutter_onegate/services/auth_service/token_notification_service.dart';

/// Optimized authentication interceptor with performance enhancements
/// Reduces token refresh overhead and improves request processing speed
class OptimizedAuthInterceptor extends Interceptor {
  final OptimizedTokenRefreshManager _tokenManager;
  final Function()? onAuthenticationFailed;
  final TokenNotificationService _notificationService = TokenNotificationService();

  // Performance optimizations
  static const int _maxRetryAttempts = 1; // Reduced from 2
  static const Duration _retryDelay = Duration(milliseconds: 200); // Reduced from 500ms
  static const Duration _tokenCacheTimeout = Duration(seconds: 30); // Token cache timeout
  
  // Token cache for request-level optimization
  String? _cachedToken;
  DateTime? _tokenCacheTime;
  
  // Performance metrics
  int _totalRequests = 0;
  int _tokenRefreshRequests = 0;
  int _cacheHits = 0;

  OptimizedAuthInterceptor({
    required OptimizedTokenRefreshManager tokenManager,
    this.onAuthenticationFailed,
  }) : _tokenManager = tokenManager;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final requestStopwatch = Stopwatch()..start();
    _totalRequests++;
    
    try {
      // Skip auth for certain endpoints
      if (_shouldSkipAuth(options.path)) {
        requestStopwatch.stop();
        handler.next(options);
        return;
      }

      // Get valid access token with caching optimization
      final accessToken = await _getOptimizedAccessToken();

      if (accessToken != null) {
        options.headers['Authorization'] = 'Bearer $accessToken';
        
        // Add performance headers
        options.headers['Connection'] = 'keep-alive';
        options.headers['Cache-Control'] = 'no-cache';
        
        requestStopwatch.stop();
        log("⚡ Auth header added in ${requestStopwatch.elapsedMilliseconds}ms: ${options.path}");
      } else {
        requestStopwatch.stop();
        log("⚠️ No valid access token available for request: ${options.path}");
      }

      // Add default headers for performance
      options.headers['Content-Type'] ??= 'application/json';
      options.headers['Accept'] ??= 'application/json';
      
    } catch (e) {
      requestStopwatch.stop();
      log("❌ Error adding auth headers: $e (${requestStopwatch.elapsedMilliseconds}ms)");
    }

    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Handle 401 Unauthorized errors with optimized retry
    if (err.response?.statusCode == 401) {
      log("🔄 Received 401 Unauthorized for: ${err.requestOptions.path}");

      final retryResult = await _handleUnauthorizedErrorOptimized(err);
      if (retryResult != null) {
        handler.resolve(retryResult);
        return;
      }
    }

    // Handle other authentication-related errors
    if (_isAuthenticationError(err)) {
      log("🚫 Authentication error detected: ${err.response?.statusCode}");
      await _handleAuthenticationFailure();
    }

    handler.next(err);
  }

  /// Get optimized access token with request-level caching
  Future<String?> _getOptimizedAccessToken() async {
    // Check request-level cache first
    if (_cachedToken != null && _tokenCacheTime != null) {
      final cacheAge = DateTime.now().difference(_tokenCacheTime!);
      if (cacheAge <= _tokenCacheTimeout) {
        _cacheHits++;
        return _cachedToken;
      }
    }

    // Get token from optimized manager
    final token = await _tokenManager.getValidAccessTokenOptimized();
    
    // Update cache
    if (token != null) {
      _cachedToken = token;
      _tokenCacheTime = DateTime.now();
    }
    
    return token;
  }

  /// Handle 401 Unauthorized errors with optimized performance
  Future<Response?> _handleUnauthorizedErrorOptimized(DioException error) async {
    final retryStopwatch = Stopwatch()..start();
    
    try {
      // Check if we should retry this request
      final retryCount = error.requestOptions.extra['retry_count'] ?? 0;
      if (retryCount >= _maxRetryAttempts) {
        retryStopwatch.stop();
        log("❌ Max retry attempts reached for: ${error.requestOptions.path}");
        return null;
      }

      log("🔄 Attempting optimized token refresh for 401 error (attempt ${retryCount + 1}/$_maxRetryAttempts)");
      _tokenRefreshRequests++;

      // Clear cached token
      _cachedToken = null;
      _tokenCacheTime = null;

      // Attempt to refresh the token with optimized manager
      final refreshed = await _tokenManager.refreshTokenOptimized();
      if (!refreshed) {
        retryStopwatch.stop();
        log("❌ Optimized token refresh failed for 401 error");
        await _handleAuthenticationFailure();
        return null;
      }

      // Get the new access token
      final newAccessToken = await _tokenManager.getValidAccessTokenOptimized();
      if (newAccessToken == null) {
        retryStopwatch.stop();
        log("❌ No valid access token after optimized refresh");
        await _handleAuthenticationFailure();
        return null;
      }

      // Update cache with new token
      _cachedToken = newAccessToken;
      _tokenCacheTime = DateTime.now();

      // Update the request with new token and retry count
      final requestOptions = error.requestOptions;
      requestOptions.headers['Authorization'] = 'Bearer $newAccessToken';
      requestOptions.extra['retry_count'] = retryCount + 1;

      log("🔄 Retrying request with new token: ${requestOptions.path}");

      // Minimal delay for optimized performance
      await Future.delayed(_retryDelay);

      // Create optimized Dio instance for retry
      final retryDio = _createOptimizedRetryDio();

      // Copy essential options
      retryDio.options.baseUrl = requestOptions.baseUrl;

      // Retry the request
      final response = await retryDio.fetch(requestOptions);
      
      retryStopwatch.stop();
      log("✅ Optimized request retry successful in ${retryStopwatch.elapsedMilliseconds}ms: ${requestOptions.path}");

      return response;
    } catch (e) {
      retryStopwatch.stop();
      log("❌ Error handling optimized 401 unauthorized: $e (${retryStopwatch.elapsedMilliseconds}ms)");
      return null;
    }
  }

  /// Create optimized Dio instance for retries
  Dio _createOptimizedRetryDio() {
    final dio = Dio();
    
    // Aggressive timeout settings for retries
    dio.options.connectTimeout = const Duration(seconds: 5);
    dio.options.receiveTimeout = const Duration(seconds: 10);
    dio.options.sendTimeout = const Duration(seconds: 5);
    
    // Performance headers
    dio.options.headers = {
      'Connection': 'keep-alive',
      'Accept-Encoding': 'gzip, deflate',
    };
    
    return dio;
  }

  /// Handle authentication failure with session expiration
  Future<void> _handleAuthenticationFailure() async {
    try {
      log("🚪 Authentication failed, clearing tokens and notifying app");

      // Check if this is a complete session expiration
      final refreshToken = await _tokenManager.getRefreshToken();
      if (refreshToken == null || refreshToken.isEmpty) {
        // Complete session expiration - show animated bottom sheet
        await _notificationService.showSessionExpired(
          errorMessage: "Your session has expired for security reasons. Please log in again to continue.",
        );
      } else {
        // Partial authentication failure - show regular error notification
        _notificationService.showAuthenticationError(
            errorMessage: "Session expired. Please login again.");
      }

      // Clear all tokens and cache
      await _tokenManager.clearTokens();
      _cachedToken = null;
      _tokenCacheTime = null;

      // Notify the app about authentication failure
      if (onAuthenticationFailed != null) {
        onAuthenticationFailed!();
      }
    } catch (e) {
      log("❌ Error handling authentication failure: $e");
      
      // Fallback to session expired bottom sheet for any authentication failure
      await _notificationService.showSessionExpired(
        errorMessage: "Authentication error occurred. Please log in again.",
      );
    }
  }

  /// Check if the request path should skip authentication
  bool _shouldSkipAuth(String path) {
    final skipAuthPaths = [
      '/auth/',
      '/login',
      '/token',
      '/refresh',
      '/public/',
      '/.well-known/',
      '/health',
      '/status',
    ];

    return skipAuthPaths.any((skipPath) => path.contains(skipPath));
  }

  /// Check if the error is authentication-related
  bool _isAuthenticationError(DioException error) {
    final authErrorCodes = [401, 403];
    return authErrorCodes.contains(error.response?.statusCode);
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    final cacheHitRate = _totalRequests > 0 ? (_cacheHits / _totalRequests * 100) : 0;
    final refreshRate = _totalRequests > 0 ? (_tokenRefreshRequests / _totalRequests * 100) : 0;
    
    return {
      'totalRequests': _totalRequests,
      'tokenRefreshRequests': _tokenRefreshRequests,
      'cacheHits': _cacheHits,
      'cacheHitRate': cacheHitRate,
      'refreshRate': refreshRate,
      'cachedTokenActive': _cachedToken != null,
    };
  }

  /// Log performance metrics
  void logPerformanceMetrics() {
    final stats = getPerformanceStats();
    log("📊 OPTIMIZED AUTH INTERCEPTOR METRICS:");
    log("   • Total Requests: ${stats['totalRequests']}");
    log("   • Token Refresh Requests: ${stats['tokenRefreshRequests']}");
    log("   • Cache Hits: ${stats['cacheHits']}");
    log("   • Cache Hit Rate: ${stats['cacheHitRate'].toStringAsFixed(1)}%");
    log("   • Refresh Rate: ${stats['refreshRate'].toStringAsFixed(1)}%");
    log("   • Cached Token Active: ${stats['cachedTokenActive']}");
  }

  /// Clear cache and reset metrics
  void resetCache() {
    _cachedToken = null;
    _tokenCacheTime = null;
    _totalRequests = 0;
    _tokenRefreshRequests = 0;
    _cacheHits = 0;
    log("🧹 Optimized auth interceptor cache and metrics reset");
  }
}

/// Optimized authentication interceptor factory
class OptimizedAuthInterceptorFactory {
  static OptimizedAuthInterceptor create({
    required OptimizedTokenRefreshManager tokenManager,
    Function()? onAuthenticationFailed,
  }) {
    return OptimizedAuthInterceptor(
      tokenManager: tokenManager,
      onAuthenticationFailed: onAuthenticationFailed,
    );
  }

  /// Create interceptor with default authentication failure handler
  static OptimizedAuthInterceptor createWithDefaultHandler({
    required OptimizedTokenRefreshManager tokenManager,
    Function()? customHandler,
  }) {
    return OptimizedAuthInterceptor(
      tokenManager: tokenManager,
      onAuthenticationFailed: customHandler ?? _defaultAuthFailureHandler,
    );
  }

  /// Default authentication failure handler
  static void _defaultAuthFailureHandler() {
    log("🚪 Default optimized auth failure handler: User needs to re-authenticate");
  }
}

/// Request options extension for optimized auth-related metadata
extension OptimizedAuthRequestOptions on RequestOptions {
  /// Mark request to debug token information
  void enableTokenDebug() {
    extra['debug_token'] = true;
  }

  /// Get retry count for this request
  int get retryCount => extra['retry_count'] ?? 0;

  /// Set retry count for this request
  set retryCount(int count) => extra['retry_count'] = count;

  /// Check if this request should skip authentication
  bool get skipAuth => extra['skip_auth'] == true;

  /// Mark request to skip authentication
  void setSkipAuth() {
    extra['skip_auth'] = true;
  }

  /// Mark request as performance critical
  void setPerformanceCritical() {
    extra['performance_critical'] = true;
  }

  /// Check if request is performance critical
  bool get isPerformanceCritical => extra['performance_critical'] == true;
}
