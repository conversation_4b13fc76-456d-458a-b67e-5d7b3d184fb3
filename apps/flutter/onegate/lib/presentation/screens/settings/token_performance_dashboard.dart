import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';
import 'package:flutter_onegate/services/auth_service/token_performance_monitor.dart';
import 'package:flutter_onegate/services/auth_service/optimized_token_refresh_manager.dart';

/// Performance dashboard for monitoring token refresh performance
class TokenPerformanceDashboard extends StatefulWidget {
  const TokenPerformanceDashboard({Key? key}) : super(key: key);

  @override
  State<TokenPerformanceDashboard> createState() => _TokenPerformanceDashboardState();
}

class _TokenPerformanceDashboardState extends State<TokenPerformanceDashboard> {
  late final AuthService _authService;
  late final TokenPerformanceMonitor _performanceMonitor;
  late final OptimizedTokenRefreshManager _optimizedManager;
  
  Map<String, dynamic>? _performanceReport;
  Map<String, dynamic>? _optimizedStats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _authService = GetIt.I<AuthService>();
    _performanceMonitor = _authService.performanceMonitor;
    _optimizedManager = _authService.optimizedTokenRefreshManager;
    _loadPerformanceData();
  }

  Future<void> _loadPerformanceData() async {
    setState(() => _isLoading = true);
    
    try {
      final report = _performanceMonitor.getPerformanceReport();
      final stats = _optimizedManager.getPerformanceStats();
      
      setState(() {
        _performanceReport = report;
        _optimizedStats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading performance data: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Token Performance Dashboard'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPerformanceData,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'test_refresh',
                child: Text('Test Token Refresh'),
              ),
              const PopupMenuItem(
                value: 'clear_metrics',
                child: Text('Clear Metrics'),
              ),
              const PopupMenuItem(
                value: 'export_data',
                child: Text('Export Data'),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadPerformanceData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPerformanceOverview(),
                    const SizedBox(height: 20),
                    _buildPerformanceMetrics(),
                    const SizedBox(height: 20),
                    _buildOptimizationStats(),
                    const SizedBox(height: 20),
                    _buildRecommendations(),
                    const SizedBox(height: 20),
                    _buildPerformanceActions(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildPerformanceOverview() {
    if (_performanceReport == null) return const SizedBox.shrink();

    final grade = _performanceReport!['performanceGrade'] ?? 'N/A';
    final last24Hours = _performanceReport!['last24Hours'] ?? {};
    final successRate = (last24Hours['successRate'] ?? 0.0).toDouble();
    final avgDuration = (last24Hours['averageDuration'] ?? 0.0).toDouble();

    Color gradeColor;
    switch (grade) {
      case 'A':
        gradeColor = Colors.green;
        break;
      case 'B':
        gradeColor = Colors.lightGreen;
        break;
      case 'C':
        gradeColor = Colors.orange;
        break;
      case 'F':
        gradeColor = Colors.red;
        break;
      default:
        gradeColor = Colors.grey;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.speed, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Performance Overview',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Performance Grade',
                    grade,
                    gradeColor,
                    Icons.grade,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Success Rate',
                    '${successRate.toStringAsFixed(1)}%',
                    successRate >= 95 ? Colors.green : successRate >= 90 ? Colors.orange : Colors.red,
                    Icons.check_circle,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    'Avg Duration',
                    '${avgDuration.toStringAsFixed(0)}ms',
                    avgDuration <= 2000 ? Colors.green : avgDuration <= 3000 ? Colors.orange : Colors.red,
                    Icons.timer,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMetricCard(
                    'Total Refreshes',
                    '${last24Hours['totalRefreshes'] ?? 0}',
                    Colors.blue,
                    Icons.refresh,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    if (_performanceReport == null) return const SizedBox.shrink();

    final last24Hours = _performanceReport!['last24Hours'] ?? {};
    final thresholds = _performanceReport!['thresholds'] ?? {};

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Performance Metrics (24h)',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMetricRow('Total Refreshes', '${last24Hours['totalRefreshes'] ?? 0}'),
            _buildMetricRow('Successful', '${last24Hours['successfulRefreshes'] ?? 0}'),
            _buildMetricRow('Failed', '${last24Hours['failedRefreshes'] ?? 0}'),
            _buildMetricRow('Min Duration', '${last24Hours['minDuration'] ?? 0}ms'),
            _buildMetricRow('Max Duration', '${last24Hours['maxDuration'] ?? 0}ms'),
            const Divider(),
            const Text('Performance Thresholds:', style: TextStyle(fontWeight: FontWeight.bold)),
            _buildMetricRow('Target', '${thresholds['target'] ?? 0}ms', Colors.green),
            _buildMetricRow('Warning', '${thresholds['warning'] ?? 0}ms', Colors.orange),
            _buildMetricRow('Critical', '${thresholds['critical'] ?? 0}ms', Colors.red),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value, [Color? valueColor]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizationStats() {
    if (_optimizedStats == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.rocket_launch, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Optimization Statistics',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMetricRow('Total Attempts', '${_optimizedStats!['totalAttempts'] ?? 0}'),
            _buildMetricRow('Successful Refreshes', '${_optimizedStats!['successfulRefreshes'] ?? 0}'),
            _buildMetricRow('Success Rate', '${(_optimizedStats!['successRate'] ?? 0.0).toStringAsFixed(1)}%'),
            _buildMetricRow('Average Duration', '${(_optimizedStats!['averageDuration'] ?? 0.0).toStringAsFixed(0)}ms'),
            _buildMetricRow('Last Refresh Duration', '${_optimizedStats!['lastRefreshDuration'] ?? 0}ms'),
            _buildMetricRow('Cache Status', '${_optimizedStats!['cacheHitRate'] ?? 'Inactive'}'),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendations() {
    if (_performanceReport == null) return const SizedBox.shrink();

    final recommendations = _performanceReport!['recommendations'] as List<String>? ?? [];

    if (recommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.amber[700]),
                const SizedBox(width: 8),
                const Text(
                  'Performance Recommendations',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...recommendations.map((recommendation) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    recommendation.contains('Critical') ? Icons.error :
                    recommendation.contains('Warning') ? Icons.warning :
                    Icons.info,
                    size: 16,
                    color: recommendation.contains('Critical') ? Colors.red :
                           recommendation.contains('Warning') ? Colors.orange :
                           Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  Expanded(child: Text(recommendation)),
                ],
              ),
            )).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Performance Actions',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _testTokenRefresh,
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Test Refresh'),
                ),
                ElevatedButton.icon(
                  onPressed: _clearMetrics,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Metrics'),
                ),
                ElevatedButton.icon(
                  onPressed: _exportData,
                  icon: const Icon(Icons.download),
                  label: const Text('Export Data'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'test_refresh':
        _testTokenRefresh();
        break;
      case 'clear_metrics':
        _clearMetrics();
        break;
      case 'export_data':
        _exportData();
        break;
    }
  }

  Future<void> _testTokenRefresh() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Testing token refresh...')),
      );

      final stopwatch = Stopwatch()..start();
      final token = await _authService.getValidAccessTokenOptimized();
      stopwatch.stop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            token != null 
                ? 'Token refresh successful in ${stopwatch.elapsedMilliseconds}ms'
                : 'Token refresh failed',
          ),
          backgroundColor: token != null ? Colors.green : Colors.red,
        ),
      );

      await _loadPerformanceData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Test failed: $e')),
      );
    }
  }

  void _clearMetrics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Metrics'),
        content: const Text('Are you sure you want to clear all performance metrics?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _performanceMonitor.clearMetrics();
              Navigator.pop(context);
              _loadPerformanceData();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Metrics cleared')),
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    try {
      final data = _performanceMonitor.exportMetrics();
      // In a real app, you would save this to a file or share it
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Exported ${data['refreshMetrics'].length} refresh metrics'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Export failed: $e')),
      );
    }
  }
}
