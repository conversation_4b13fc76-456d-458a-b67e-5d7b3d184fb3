// import 'package:flutter/foundation.dart';
// import 'package:flutter/material.dart';
// import 'package:mobile_scanner/mobile_scanner.dart';

// class QRScan extends StatefulWidget {
//   const QRScan({super.key});

//   @override
//   State<QRScan> createState() => _QRScanState();
// }

// class _QRScanState extends State<QRScan> {
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(title: const Text('Mobile Scanner')),
//       body: MobileScanner(
//         // fit: BoxFit.contain,
//         controller: MobileScannerController(
//           detectionSpeed: DetectionSpeed.normal,
//           facing: CameraFacing.front,
//           torchEnabled: true,
//         ),
//         onDetect: (capture) {
//           final List<Barcode> barcodes = capture.barcodes;
//           final Uint8List? image = capture.image;
//           for (final barcode in barcodes) {
//             debugPrint('Barcode found! ${barcode.rawValue}');
//           }
//         },
//       ),
//     );
//   }
// }
