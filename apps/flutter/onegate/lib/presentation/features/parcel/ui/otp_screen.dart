// import 'package:common_widgets/common_widgets.dart';
// import 'package:flutter/material.dart';
//
// class OtpScreen extends StatelessWidget {
//   final Map<String, dynamic> otpData;
//
//   OtpScreen({Key? key, required this.otpData}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return MyScrollView(
//       pageTitle: 'OTP',
//       pageBody: Center(
//         child: Text('OTP: ${otpData['otp']}'),
//       ),
//     );
//   }
// }
