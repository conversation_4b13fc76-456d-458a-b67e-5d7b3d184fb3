// ignore_for_file: prefer_const_constructors

import 'dart:developer';

import 'package:common_widgets/common_widgets.dart';
import 'package:common_widgets/loading_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_onegate/data/datasources/gate_storage.dart';
import 'package:flutter_onegate/data/datasources/remote_datasource.dart';
import 'package:flutter_onegate/data/repositories/visitor_log_repo_impl.dart';
import 'package:flutter_onegate/domain/entities/visitor/visitorLog.dart';
import 'package:flutter_onegate/domain/use_cases/visitor_log_usecae.dart';
import 'package:flutter_onegate/presentation/features/dashboard/gatekeeper/pages/gatekeeper_dashboard_view.dart';
import 'package:flutter_onegate/presentation/features/gate_selection/ui/gate_selection_provider.dart';
import 'package:flutter_onegate/presentation/features/gate_selection/ui/gate_selection_view.dart';
import 'package:flutter_onegate/presentation/features/visitor_log/bloc/visitor_log_bloc.dart';
import 'package:flutter_onegate/presentation/features/visitor_log/ui/visitor_Details.dart';
import 'package:flutter_onegate/utils/app_utils.dart';
import 'package:flutter_onegate/utils/myfluttertoast.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:lottie/lottie.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_onegate/presentation/widgets/building_dropdown.dart';

class VisitorLogView extends StatefulWidget {
  String id;
  final List<String> logList;
  final String? selectedBuilding;
  int? societyID;

  VisitorLogView({
    required this.id,
    required this.logList,
    this.selectedBuilding,
    Key? key,
  }) : super(key: key);

  @override
  State<VisitorLogView> createState() => _VisitorLogViewState();
}

class _VisitorLogViewState extends State<VisitorLogView> {
  late String selectedId;
  String? _searchText = "";
  var selectedGateName;
  late FocusNode _searchFocusNode;
  String? selectedBuilding = "All Buildings";

  List<String> options = ['All', 'Today', 'This Week', 'This Month', 'Custom'];
  final gateStorage = GateStorage();
  final remoteDataSource = RemoteDataSource();
  var societyId;
  final ScrollController _scrollController = ScrollController();

  final VisitorLogBloc _visitorLogBloc = VisitorLogBloc(
    VisitorLogUsecase(
      VisitorLogRepositoryImpl(
        RemoteDataSource(),
      ),
    ),
  );

  @override
  void initState() {
    super.initState();
    selectedId = widget.id;

    switch (widget.id) {
      case "In Out Book":
        _visitorLogBloc.add(FetchVisitorLogEvent(
          Utils.getCurrentTime(),
          currentPage: 1,
          perPage: 20,
        ));
        break;
      case "Visitor In":
        _visitorLogBloc.add(FetchCheckInLogEvent(
          Utils.getCurrentTime(),
          currentPage: 1,
          perPage: 20,
        ));
        break;
      case "Cards":
        _visitorLogBloc.add(FetchCheckOutLogEvent(
          Utils.getCurrentTime(),
          currentPage: 1,
          perPage: 20,
        ));
        break;

      case "Visitor Out":
        _visitorLogBloc.add(FetchCheckOutLogEvent(Utils.getCurrentTime()));
        break;
    }
    _initializeSocietyId();
    getSelectedGate();
    _searchFocusNode = FocusNode();
    // _scrollController.addListener(() {
    //   if (_scrollController.position.pixels >=
    //           _scrollController.position.maxScrollExtent - 200 &&
    //       _visitorLogBloc.state is! VisitorLogLoadingMoreState) {
    //     final currentState = _visitorLogBloc.state;
    //     if (currentState is VisitorLogSuccessState) {
    //       _visitorLogBloc.add(LoadMoreVisitorLogsEvent(
    //         currentState.currentPage! + 1, // Pass next page
    //         40, // Number of items per page
    //       ));
    //     }
    //   }
    // });
    // _storeTodayLogsCount(context);
  }

  int current_page = 5;
  final int per_page = 20;

  @override
  void dispose() {
    _searchFocusNode.dispose();

    super.dispose();
  }

  Future<void> getSelectedGate() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      selectedGateName = prefs.getString('selected_gate');
    });
  }

  Future<void> _initializeSocietyId() async {
    societyId = await gateStorage.getSocietyId();
    log('Society ID: $societyId');
  }

  @override
  Widget build(BuildContext context) {
    final today = DateTime.now();
    final yesterday = today.subtract(Duration(days: 1));

    return BlocConsumer<VisitorLogBloc, VisitorLogState>(
      bloc: _visitorLogBloc,
      listenWhen: (previous, current) => current is VisitorLogActionState,
      buildWhen: (previous, current) => current is! VisitorLogActionState,
      listener: (context, state) {
        switch (state.runtimeType) {
          case VisitorLogCheckOutSuccessState:
            final successState = state as VisitorLogCheckOutSuccessState;
            if (successState.isCheckOut!) {
              myFluttertoast(
                msg: "User Checked Out Successfully",
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.CENTER,
                timeInSecForIosWeb: 1,
                backgroundColor: Colors.red,
                textColor: Colors.white,
                fontSize: 16.0,
              );
              _visitorLogBloc.add(FetchVisitorLogEvent(DateTime.now()));
            }
            break;
          case VisitorCheckInLogSuccessState:
            _visitorLogBloc.add(FetchCheckInLogEvent(Utils.getCurrentTime()));
            Fluttertoast.showToast(
              msg: "Visitor Checked Out Successfully",
              toastLength: Toast.LENGTH_SHORT,
              gravity: ToastGravity.CENTER,
              timeInSecForIosWeb: 1,
              backgroundColor: Colors.green,
              textColor: Colors.white,
              fontSize: 16.0,
            );
            break;
        }
      },
      builder: (context, state) {
        switch (state.runtimeType) {
          case VisitorLogLoadingState:
            return LoaderView();
          case VisitorLogSuccessState:
            final successState = state as VisitorLogSuccessState;
            final visitorLogs = (successState.visitorLogs ?? []).where((log) {
              if (widget.id == "Cards") {
                return log.visitor_card_number != null &&
                    log.visitor_card_number!.isNotEmpty;
              }
              return true; // Show all logs for other cases
            }).toList();
            List<VisitorLog> uniqueVisitorLogs = [];
            Set<String> checkInTimes = {};

            for (var log in visitorLogs) {
              final checkInTime = log.visitor_check_in?.toIso8601String();
              if (!checkInTimes.contains(checkInTime)) {
                checkInTimes.add(checkInTime!);
                uniqueVisitorLogs.add(log);
              }
            }

            // // Extract building names from visitor logs using the helper method
            // Set<String> buildingNames = BuildingDropdown.extractBuildingNames(
            //   uniqueVisitorLogs,
            //   getUnitName: (VisitorLog log) {
            //     if (log.visitor_building_assignment != null &&
            //         log.visitor_building_assignment!.isNotEmpty &&
            //         log.visitor_building_assignment!.first.unit_id != null &&
            //         log.visitor_building_assignment!.first.unit_id!
            //             .isNotEmpty) {
            //       return log.visitor_building_assignment!.first.unit_id!.first;
            //     }
            //     return "";
            //   },
            // );

            // List<String> sortedBuildingNames = buildingNames.toList();

            // Filter visitors by name search
            List<VisitorLog> filteredVisitors = uniqueVisitorLogs
                .where((visitorLog) => visitorLog.visitor!.name!
                    .toLowerCase()
                    .contains(_searchText!.toLowerCase()))
                .toList();

            // Filter by selected building if not "All Buildings"
            if (selectedBuilding != null &&
                selectedBuilding != "All Buildings") {
              filteredVisitors = filteredVisitors.where((log) {
                if (log.visitor_building_assignment != null &&
                    log.visitor_building_assignment!.isNotEmpty &&
                    log.visitor_building_assignment!.first.unit_id != null &&
                    log.visitor_building_assignment!.first.unit_id!
                        .isNotEmpty) {
                  String unitId =
                      log.visitor_building_assignment!.first.unit_id!.first;
                  if (unitId.contains("-")) {
                    String buildingName = unitId.split("-")[0].trim();
                    return buildingName == selectedBuilding;
                  } else {
                    return unitId == selectedBuilding;
                  }
                }
                return false;
              }).toList();
            }

            final today = DateTime.now();
            final startOfToday = DateTime(today.year, today.month, today.day);
            final endOfToday = startOfToday.add(const Duration(days: 1));
            final startOfYesterday =
                startOfToday.subtract(const Duration(days: 1));
            final endOfYesterday = startOfToday;

            List<VisitorLog> todayLogs = filteredVisitors.where((log) {
              final checkInDate = log.visitor_check_in!;
              return checkInDate.isAfter(startOfToday) &&
                  checkInDate.isBefore(endOfToday);
            }).toList();

            List<VisitorLog> todayCheckoutLogs = filteredVisitors.where((log) {
              final checkOutDate = log.visitor_check_out;
              return checkOutDate != null &&
                  checkOutDate.isAfter(startOfToday) &&
                  checkOutDate.isBefore(endOfToday);
            }).toList();

            Future<void> storeTodayLogsCount(int count, String key) async {
              final prefs = await SharedPreferences.getInstance();
              prefs.setInt(key, count);
            }

            storeTodayLogsCount(todayLogs.length, 'todayLogsCount');
            storeTodayLogsCount(
                todayCheckoutLogs.length, 'todayCheckoutLogsCount');

            List<VisitorLog> yesterdayLogs = filteredVisitors.where((log) {
              final checkInDate = log.visitor_check_in!;
              return checkInDate.isAfter(startOfYesterday) &&
                  checkInDate.isBefore(endOfYesterday);
            }).toList();

            List<VisitorLog> olderLogs = filteredVisitors.where((log) {
              final checkInDate = log.visitor_check_in!;
              return checkInDate.isBefore(startOfYesterday);
            }).toList();
            bool isPopping = false;

            Map<String, List<VisitorLog>> groupedLogs = {};
            for (var log in filteredVisitors) {
              String dateKey =
                  DateFormat('yyyy-MM-dd').format(log.visitor_check_in!);
              if (!groupedLogs.containsKey(dateKey)) {
                groupedLogs[dateKey] = [];
              }
              groupedLogs[dateKey]!.add(log);
            }

// Convert the map to a list of entries
            List<MapEntry<String, List<VisitorLog>>> groupedLogsList =
                groupedLogs.entries.toList();

// Sort the list by date (most recent first)
            groupedLogsList.sort((a, b) => b.key.compareTo(a.key));

            return WillPopScope(
              onWillPop: () async {
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(builder: (context) => GateDashboardView()),
                  (Route<dynamic> route) => false,
                );
                return false;
              },
              child: MyScrollView(
                // isScrollable: false,
                hasBackButton: false,
                pageTitleWidget: Hero(
                  tag: 'page_title',
                  child: Text(
                    widget.id,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ),
                actions: [
                  if (widget.id == "In Out Book")
                    Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Theme.of(context).dividerColor,
                          ),
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: GestureDetector(
                          onTap: () async {
                            await _showExportBottomSheet(context, visitorLogs);
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.download_rounded,
                                  color: Colors.black,
                                ),
                                Padding(
                                  padding: EdgeInsets.only(right: 8.0),
                                  child: Text("Export",
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                ],
                pageBody: SizedBox(
                  height: MediaQuery.of(context).size.height,
                  child: Column(
                    children: [
                      Column(
                        children: [
                          // Building selection dropdown
                          // Search field
                          CustomForm.textField(
                            widget.selectedBuilding ?? 'Search',
                            focusNode: _searchFocusNode,
                            titleColor: Theme.of(context).colorScheme.onSurface,
                            hintColor: Theme.of(context).colorScheme.onSurface,
                            hintText: 'Search Visitor',
                            textCapitalization: TextCapitalization.words,
                            textInputAction: TextInputAction.search,
                            onFieldSubmitted: (value) {
                              log(value);
                            },
                            onChanged: (value) {
                              setState(() {
                                _searchText = value;
                              });
                            },
                            prefixIcon: IconButton(
                              onPressed: () {},
                              icon: Icon(
                                Ionicons.search_outline,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            suffixIcon: IconButton(
                              onPressed: () {
                                _showLogBookConfigBottomSheet(context);
                              },
                              icon: Icon(
                                Ionicons.funnel_outline,
                                color: Theme.of(context).colorScheme.onSurface,
                                size: 24,
                              ),
                            ),
                          ),
                          // BuildingDropdown(
                          //   selectedBuilding: selectedBuilding,
                          //   onBuildingSelected: (String? value) {
                          //     setState(() {
                          //       selectedBuilding = value;
                          //     });
                          //   },
                          //   buildingNames: sortedBuildingNames,
                          // ),
                        ],
                      ),
                      if (_searchText!.isNotEmpty && filteredVisitors.isEmpty)
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person_off_outlined,
                                size: 48,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.6),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No such visitors found in log',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withOpacity(0.7),
                                      fontSize: 14,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      if (filteredVisitors.isEmpty && _searchText!.isEmpty)
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person_off_outlined,
                                size: 48,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.6),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No visitors today',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w500,
                                    ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'When visitors check in, they will appear here',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withOpacity(0.7),
                                      fontSize: 14,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      Expanded(
                        child: ListView.builder(
                          padding: EdgeInsets.only(bottom: 100),
                          physics: BouncingScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: groupedLogsList.length,
                          itemBuilder: (context, index) {
                            final dateKey = groupedLogsList[index].key;
                            final logsForDate = groupedLogsList[index].value;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0, vertical: 8.0),
                                  child: Chip(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                          25), // Adjust the radius as needed
                                      side: BorderSide.none, // No border
                                    ),
                                    side: BorderSide.none,
                                    label: Text(
                                      DateFormat('MMM dd, yyyy')
                                          .format(DateTime.parse(dateKey)),
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelSmall,
                                    ),
                                    backgroundColor:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                Builder(builder: (context) {
                                  // No sorting - display logs as they come from the API response

                                  return ListView.builder(
                                    padding: EdgeInsets.zero,
                                    physics: NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemCount: logsForDate.length,
                                    itemBuilder: (context, logIndex) {
                                      return VisitorLogItem(
                                        visitorLog: logsForDate[logIndex],
                                        onCheckOut: () {
                                          setState(() {
                                            logsForDate[logIndex]
                                                    .visitor_check_out =
                                                Utils.getCurrentTime();
                                            logsForDate[logIndex]
                                                .is_checked_out = true;
                                          });
                                          // Use add instead of emit
                                          _visitorLogBloc.add(CheckOutEvent(
                                            logsForDate[logIndex],
                                            widget.id,
                                          ));
                                        },
                                      );
                                    },
                                  );
                                }),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          default:
            return Container();
        }
      },
    );
  }

  bool isLoading = false;

  Future<void> _showExportBottomSheet(
      BuildContext context, List<VisitorLog> visitorLogs) async {
    TextEditingController emailController = TextEditingController();
    TextEditingController nameController = TextEditingController();
    DateTime? startDate;
    DateTime? endDate;

    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final storedEmail = prefs.getString('email') ?? '';
    emailController.text = storedEmail;

    final exportFormKey = GlobalKey<FormState>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              padding: EdgeInsets.only(
                top: 16,
                left: 16,
                right: 16,
                bottom: MediaQuery.of(context).viewInsets.bottom + 16,
              ),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Form(
                key: exportFormKey,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Text(
                            'Export Logs',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          IconButton(
                              padding: const EdgeInsets.only(bottom: 15),
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              icon: const Icon(
                                Icons.cancel,
                                color: Colors.red,
                                size: 25,
                              ))
                        ],
                      ),

                      const SizedBox(height: 16),
                      CustomForm.textField(
                        "Email",
                        titleColor: Theme.of(context).colorScheme.onSurface,
                        hintColor: Theme.of(context).colorScheme.onPrimary,
                        hintText: "Enter email for export",
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return 'Please enter email';
                          }
                          if (!value.contains('@')) {
                            return 'Please enter a valid email';
                          }
                          return null;
                        },
                        textController: emailController,
                      ),
                      const SizedBox(height: 20),
                      // Start Date with Validation
                      FormField<DateTime>(
                        validator: (value) {
                          if (startDate == null) {
                            return "Please select a start date";
                          }
                          return null;
                        },
                        builder: (fieldState) {
                          return InkWell(
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: startDate ?? DateTime.now(),
                                firstDate: DateTime(2000),
                                lastDate: DateTime.now(),
                                builder: (BuildContext context, Widget? child) {
                                  return Theme(
                                    data: ThemeData.light().copyWith(
                                      colorScheme: ColorScheme.light(
                                        primary: Colors
                                            .black, // Primary color for dialog
                                      ),
                                      textButtonTheme: TextButtonThemeData(
                                        style: TextButton.styleFrom(
                                          foregroundColor: Colors
                                              .black, // Black text for "Cancel" and "OK"
                                        ),
                                      ),
                                    ),
                                    child: child!,
                                  );
                                },
                              );
                              if (picked != null) {
                                setState(() {
                                  startDate = picked;
                                  // Ensure "To Date" is valid
                                  if (endDate != null &&
                                      startDate!.isAfter(endDate!)) {
                                    endDate = null;
                                  }
                                  fieldState.didChange(picked);
                                });
                              }
                            },
                            child: _buildDateField(
                              context,
                              label: 'From Date',
                              date: startDate,
                              placeholder: 'Select From Date',
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 16),
                      // End Date with Validation
                      FormField<DateTime>(
                        validator: (value) {
                          if (endDate == null) {
                            return "Please select an end date";
                          }
                          if (startDate != null &&
                              endDate!.isBefore(startDate!)) {
                            return "End date cannot be earlier than start date";
                          }
                          return null;
                        },
                        builder: (fieldState) {
                          return InkWell(
                            onTap: () async {
                              final DateTime? picked = await showDatePicker(
                                context: context,
                                initialDate: endDate ?? DateTime.now(),
                                firstDate: startDate ?? DateTime(2000),
                                lastDate: DateTime.now(),
                                builder: (BuildContext context, Widget? child) {
                                  return Theme(
                                    data: ThemeData.light().copyWith(
                                      colorScheme: ColorScheme.light(
                                        primary: Colors
                                            .black, // Primary color for dialog
                                      ),
                                      textButtonTheme: TextButtonThemeData(
                                        style: TextButton.styleFrom(
                                          foregroundColor: Colors
                                              .black, // Black text for "Cancel" and "OK"
                                        ),
                                      ),
                                    ),
                                    child: child!,
                                  );
                                },
                              );
                              if (picked != null) {
                                setState(() {
                                  endDate = picked;
                                  fieldState.didChange(picked);
                                });
                              }
                            },
                            child: _buildDateField(
                              context,
                              label: 'To Date',
                              date: endDate,
                              placeholder: 'Select To Date',
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 24),
                      if (isLoading)
                        const CircularProgressIndicator(
                          color: Colors.black,
                        )
                      else
                        CustomLargeBtn(
                          onPressed: () async {
                            if (startDate == null) {
                              myFluttertoast(
                                msg: "Please select a start date",
                                toastLength: Toast.LENGTH_SHORT,
                                gravity: ToastGravity.BOTTOM,
                                timeInSecForIosWeb: 1,
                                backgroundColor: Colors.red,
                                textColor: Colors.white,
                                fontSize: 16.0,
                              );
                              return;
                            }
                            if (endDate == null) {
                              myFluttertoast(
                                msg: "Please select an end date",
                                toastLength: Toast.LENGTH_SHORT,
                                gravity: ToastGravity.BOTTOM,
                                timeInSecForIosWeb: 1,
                                backgroundColor: Colors.red,
                                textColor: Colors.white,
                                fontSize: 16.0,
                              );
                              return;
                            }
                            if (exportFormKey.currentState!.validate()) {
                              await prefs.setString(
                                  'email', emailController.text);

                              final formattedFromDate =
                                  DateFormat('yyyy-MM-dd').format(startDate!);
                              final formattedToDate =
                                  DateFormat('yyyy-MM-dd').format(endDate!);

                              final gateProvider = Provider.of<GateProvider>(
                                  context,
                                  listen: false);
                              final selectedGate = gateProvider.selectedGate;
                              final email = emailController.text.trim();

                              final visitorData = {
                                "company_id": societyId,
                                "name": nameController.text,
                                "to_mail": email,
                                "from_date": formattedFromDate,
                                "to_date": formattedToDate,
                                "in_gate": selectedGateName,
                              };

                              try {
                                await remoteDataSource.exportLogs(visitorData);
                                Navigator.pop(context);
                                showSuccessDialog(
                                    context: context,
                                    title: "Export logs",
                                    message:
                                        "Visitor logs exported successfully.");
                              } catch (e) {
                                // Handle error case
                              }
                            }
                          },
                          text: "Export",
                        ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title,
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium!
                  .copyWith(color: Colors.red)),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context), // Close dialog
              child: const Text("OK"),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDateField(BuildContext context,
      {required String label, DateTime? date, required String placeholder}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.calendar_today, size: 20),
              const SizedBox(width: 8),
              Text(
                date != null
                    ? DateFormat('MMM dd, yyyy').format(date)
                    : placeholder,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void showSuccessDialog({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onDismiss,
  }) {
    showGeneralDialog(
      context: context,
      pageBuilder: (_, __, ___) => Container(),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        final curvedAnimation = CurvedAnimation(
          parent: animation,
          curve: Curves.easeOutBack,
        );

        return ScaleTransition(
          scale: Tween<double>(begin: 0.5, end: 1.0).animate(curvedAnimation),
          child: FadeTransition(
            opacity:
                Tween<double>(begin: 0.0, end: 1.0).animate(curvedAnimation),
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.0),
              ),
              backgroundColor: Theme.of(context).colorScheme.surface,
              elevation: 8,
              title: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, -0.5),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: animation,
                  curve: const Interval(0.0, 0.7, curve: Curves.easeOutCubic),
                )),
                child: Row(
                  children: [
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 1500),
                      tween: Tween<double>(begin: 0, end: 2 * 3.14159),
                      builder: (context, value, child) => Transform.rotate(
                        angle: value,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.check_circle,
                            color: Colors.green.shade600,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                      ),
                    ),
                  ],
                ),
              ),
              content: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0, 0.5),
                  end: Offset.zero,
                ).animate(CurvedAnimation(
                  parent: animation,
                  curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
                )),
                child: FadeTransition(
                  opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                    CurvedAnimation(
                      parent: animation,
                      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Text(
                      message,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            // color: Colors.green,
                            height: 1.5,
                          ),
                    ),
                  ),
                ),
              ),
              actions: [
                SlideTransition(
                  position: Tween<Offset>(
                    begin: const Offset(0, 1),
                    end: Offset.zero,
                  ).animate(CurvedAnimation(
                    parent: animation,
                    curve: const Interval(0.3, 0.9, curve: Curves.easeOutCubic),
                  )),
                  child: FadeTransition(
                    opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: const Interval(0.3, 0.9, curve: Curves.easeOut),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8.0, bottom: 8.0),
                      child: FilledButton(
                        style: FilledButton.styleFrom(
                          backgroundColor:
                              Colors.black, // Set the background color to black
                          foregroundColor:
                              Colors.white, // Set the text color to white
                        ),
                        onPressed: () {
                          Navigator.of(context).pop();
                          onDismiss?.call();
                        },
                        child: const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.0),
                          child: Text('OK'),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      transitionDuration: const Duration(milliseconds: 500),
      barrierDismissible: false,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierColor: Colors.black54,
    );
  }

  void _showLogBookConfigBottomSheet(BuildContext context) async {
    showModalBottomSheet(
      useSafeArea: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      context: context,
      builder: (ctx) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                color: Theme.of(context).colorScheme.surface,
              ),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    title: Text(
                      'Filters',
                      style: Theme.of(context).textTheme.displaySmall!.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    itemCount: widget.logList.length,
                    itemBuilder: (context, index) {
                      return GateSettingListTile(
                        switchValue: selectedId == widget.logList[index],
                        onChanged: (value) {
                          setState(() {
                            selectedId = widget.logList[index];
                          });
                        },
                        title: widget.logList[index],
                        subtitle: 'Enable/Disable ${widget.logList[index]}',
                        leadingIcon: Symbols.gate,
                      );
                    },
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  CustomLargeBtn(
                    text: 'Confirm',
                    onPressed: () {
                      setState(() {
                        widget.id = selectedId;
                      });
                      Navigator.pop(context);
                      Navigator.pushReplacement(
                        context,
                        PageTransition(
                          type: PageTransitionType.bottomToTop,
                          child: VisitorLogView(
                            id: widget.id,
                            logList: widget.logList,
                            selectedBuilding: widget.selectedBuilding,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // Add a method to update today's counts
  Future<void> _updateTodayLogsCount() async {
    final prefs = await SharedPreferences.getInstance();
    final currentCount = prefs.getInt('todayLogsCount') ?? 0;
    await prefs.setInt('todayLogsCount', currentCount - 1);

    final currentCheckoutCount = prefs.getInt('todayCheckoutLogsCount') ?? 0;
    await prefs.setInt('todayCheckoutLogsCount', currentCheckoutCount + 1);
  }
}

class VisitorLogItem extends StatefulWidget {
  final VisitorLog visitorLog;
  final Function onCheckOut;

  const VisitorLogItem({
    Key? key,
    required this.visitorLog,
    required this.onCheckOut,
  }) : super(key: key);

  @override
  State<VisitorLogItem> createState() => _VisitorLogItemState();
}

class _VisitorLogItemState extends State<VisitorLogItem> {
  final bool _hasCallSupport = true;
  Future<void>? _launched;

  @override
  void initState() {
    log("called..............${widget.visitorLog.visitor!.name} ${widget.visitorLog.visitor_check_in}");
    super.initState();
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    await launchUrl(launchUri);
  }

  @override
  Widget build(BuildContext context) {
    String unitList = '';

    if (widget.visitorLog.visitor_building_assignment != null &&
        widget.visitorLog.visitor_building_assignment!.isNotEmpty) {
      unitList = widget.visitorLog.visitor_building_assignment!
          .expand((assignment) => assignment.unit_id ?? [])
          .map((unit) => unit.toString())
          .join(', ');
    }
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Card(
        elevation: 2,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            ListTile(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => VisitorDetailsScreen(
                      image: widget.visitorLog.visitor!.visitor_image,
                      unitList: unitList,
                      visitorLog: widget.visitorLog,
                    ),
                  ),
                );
              },
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 2,
              ),
              leading: CircleAvatar(
                backgroundImage: widget
                            .visitorLog.visitor!.visitor_image!.isNotEmpty &&
                        widget.visitorLog.visitor!.visitor_image != null
                    ? NetworkImage(
                        widget.visitorLog.visitor!.visitor_image ?? "")
                    : NetworkImage(
                        'https://images.unsplash.com/photo-1731778572747-315c9089bc69?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'),
                child: widget.visitorLog.visitor!.visitor_image!.isEmpty
                    ? Text(
                        widget.visitorLog.visitor!.name!.isNotEmpty
                            ? widget.visitorLog.visitor!.name![0]
                            : 'G',
                        style: Theme.of(context).textTheme.bodyMedium,
                      )
                    : null,
              ),
              title: Text(
                widget.visitorLog.visitor!.name ?? 'Visitor Name',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getPurposeIcon(
                            widget.visitorLog.visitor_purpose_Category_name),
                        color: Colors.grey[600],
                        size: 18,
                      ),
                      const SizedBox(width: 8), // Add spacing
                      Flexible(
                        child: Text(
                          "${_capitalizeFirstLetter(widget.visitorLog.visitor_purpose_Category_name?.toString() ?? "N/A")} ${_getUnitText()}",
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                  ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
            Divider(
              indent: 16,
              endIndent: 16,
              color: Colors.grey[200],
            ),
            Container(
              padding: const EdgeInsets.only(
                  bottom: 14.0, top: 8, left: 12, right: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Tooltip(
                    message: widget.visitorLog.visitor_check_in != null
                        ? DateFormat('dd-MM-yyyy hh:mm a')
                            .format(widget.visitorLog.visitor_check_in!)
                        : "No check-in time",
                    child: RichText(
                      text: TextSpan(
                        children: [
                          const WidgetSpan(
                            child: Icon(
                              Symbols.directions_walk_rounded,
                              color: Colors.green,
                            ),
                          ),
                          TextSpan(
                            text: widget.visitorLog.visitor_check_in != null
                                ? Utils.convertDateTimeFormat(
                                    widget.visitorLog.visitor_check_in!)
                                : "N/A",
                            style:
                                Theme.of(context).textTheme.labelMedium!.merge(
                                      Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .copyWith(
                                            color: Colors.green,
                                          ),
                                    ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  widget.visitorLog.visitor_card_number != null
                      ? Container(
                          margin: const EdgeInsets.only(left: 8),
                          padding: const EdgeInsets.symmetric(
                            vertical: 2,
                            horizontal: 10,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: const [
                                Color.fromRGBO(255, 236, 158, 0.8),
                                Color.fromRGBO(255, 190, 168, 0.8),
                              ],
                              begin: Alignment.topRight,
                              end: Alignment.bottomLeft,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Color.fromRGBO(255, 190, 168, 1),
                            ),
                          ),
                          child: Row(
                            children: [
                              widget.visitorLog.visitor_card_number != null
                                  ? Lottie.asset(
                                      'assets/json/idcard.json',
                                      width: 30,
                                      height: 30,
                                      fit: BoxFit.cover,
                                    )
                                  : Icon(
                                      Symbols.car_tag_rounded,
                                      size: 30,
                                      // color: Colors.red, // Optional color for the icon
                                    ),
                              const SizedBox(width: 5),
                              Text(
                                widget.visitorLog.visitor_card_number != null
                                    ? widget.visitorLog.visitor_card_number!
                                    : 'N/A',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                      color: Colors.black,
                                      fontWeight: FontWeight.w800,
                                      fontSize: 14,
                                    ),
                              ),
                            ],
                          ),
                        )
                      : Spacer(),
                  (widget.visitorLog.visitor_check_out.toString().isEmpty ||
                          widget.visitorLog.visitor_check_out.toString() ==
                              'null')
                      ? ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return AlertDialog(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  title: Row(
                                    children: [
                                      Icon(Icons.warning_amber_rounded,
                                          color: Colors.red),
                                      SizedBox(width: 8),
                                      Text(
                                        'Confirm Checkout',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                                    ],
                                  ),
                                  content: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Are you sure you want to checkout?',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .copyWith(),
                                      ),
                                      SizedBox(height: 8),
                                      Text(
                                        'This action cannot be undone.',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .copyWith(
                                              color: Colors.grey[600],
                                            ),
                                      ),
                                    ],
                                  ),
                                  actions: [
                                    ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.white,
                                        elevation: 0,
                                        side: BorderSide(
                                            color: Colors.grey[300]!),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                      ),
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: Text(
                                        'Cancel',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .copyWith(
                                              color: Colors.black87,
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                    ),
                                    ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red,
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                      ),
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                        widget.onCheckOut();
                                        // context
                                        //     .read<GatekeeperDashboardBloc>()
                                        //     .add(
                                        //         GatekeeperDashboardInitialEvent());
                                      },
                                      child: Text(
                                        'Checkout',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .copyWith(
                                              color: Colors.white,
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                    ),
                                  ],
                                  actionsPadding: EdgeInsets.all(16),
                                  actionsAlignment: MainAxisAlignment.end,
                                );
                              },
                            );
                          },
                          child: Text(
                            'Checkout',
                            style:
                                Theme.of(context).textTheme.labelSmall!.merge(
                                      Theme.of(context)
                                          .textTheme
                                          .bodyMedium!
                                          .copyWith(
                                            color: Colors.white,
                                          ),
                                    ),
                          ),
                        )
                      : Tooltip(
                          message: widget.visitorLog.visitor_check_out != null
                              ? DateFormat('dd-MM-yyyy hh:mm a')
                                  .format(widget.visitorLog.visitor_check_out!)
                              : "No check-out time",
                          child: RichText(
                            text: TextSpan(
                              children: [
                                const WidgetSpan(
                                  child: Icon(
                                    Symbols.directions_walk_rounded,
                                    color: Colors.red,
                                  ),
                                ),
                                TextSpan(
                                  text: widget.visitorLog.visitor_check_out !=
                                          null
                                      ? Utils.convertDateTimeFormat(
                                          widget.visitorLog.visitor_check_out!)
                                      : "N/A",
                                  style: Theme.of(context)
                                      .textTheme
                                      .labelMedium!
                                      .merge(
                                        Theme.of(context)
                                            .textTheme
                                            .bodyMedium!
                                            .copyWith(
                                              color: Colors.red,
                                            ),
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _capitalizeFirstLetter(String text) {
    if (text.isEmpty) return "";
    return text
        .split(' ') // Split into words
        .map((word) => word.isNotEmpty
            ? word[0].toUpperCase() + word.substring(1).toLowerCase()
            : word)
        .join(' '); // Join words back
  }

  IconData _getPurposeIcon(String? category) {
    switch (category?.toUpperCase()) {
      case "DELIVERY":
        return Icons.inventory_2_outlined;
      case "CABS":
        return Symbols.local_taxi;
      case "VENDOR":
        return Symbols.storefront;
      default:
        return Symbols.person;
    }
  }

  String _getUnitText() {
    // Check if visitor_building_assignment exists and has items
    if (widget.visitorLog.visitor_building_assignment == null ||
        widget.visitorLog.visitor_building_assignment!.isEmpty) {
      return ""; // Return empty string if no building assignment
    }

    // Check if the first building assignment has unit_id
    final firstAssignment =
        widget.visitorLog.visitor_building_assignment!.first;
    if (firstAssignment.unit_id == null || firstAssignment.unit_id!.isEmpty) {
      return ""; // Return empty string if no unit_id
    }

    // Return the unit_id with a dash prefix
    return "- ${firstAssignment.unit_id!.first}";
  }
}
