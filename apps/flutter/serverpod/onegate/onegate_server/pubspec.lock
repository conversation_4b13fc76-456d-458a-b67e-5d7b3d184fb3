# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  amazon_cognito_identity_dart_2:
    dependency: transitive
    description:
      name: amazon_cognito_identity_dart_2
      sha256: ad550b054e19c1879f729f9f1b612f91f21f2c595623942a283564f9218ca6c8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  buffer:
    dependency: transitive
    description:
      name: buffer
      sha256: "8962c12174f53e2e848a6acd7ac7fd63d8a1a6a316c20c458a832d87eba5422a"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: a8de5955205b4d1dbbbc267daddf2178bd737e4bab8987c04a500478c9651e74
      url: "https://pub.dev"
    source: hosted
    version: "8.6.3"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb98c0f6d12c920a02ee2d998da788bca066ca5f148492b7085ee23372b12306
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.dev"
    source: hosted
    version: "1.18.0"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  executor:
    dependency: transitive
    description:
      name: executor
      sha256: "87d935b31b2bdf7fe2af0b77dd8ffe8864eaade25715e9c68bc49497e48c9851"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.6"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  lints:
    dependency: "direct dev"
    description:
      name: lints
      sha256: "0a217c6c989d21039f1498c3ed9f3ed71b354e69873f13a8dfc3c9fe76f1b452"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: a6e590c838b18133bb482a2745ad77c5bb7715fb0451209e1a7567d416678b8e
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  mustache_template:
    dependency: transitive
    description:
      name: mustache_template
      sha256: a46e26f91445bfb0b60519be280555b06792460b27b19e2b19ad5b9740df5d1c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "8829d8a55c13fc0e37127c29fedf290c102f4e40ae94ada574091fe0ff96c917"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.3"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: eeb2d1428ee7f4170e2bd498827296a18d4e7fc462b71727d111c0ac7707cfa6
      url: "https://pub.dev"
    source: hosted
    version: "6.0.1"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  postgres:
    dependency: transitive
    description:
      name: postgres
      sha256: "98457afc06dd3f9d6892c178ea03ca9659e454107c9be90111e607691998d70d"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  postgres_pool:
    dependency: transitive
    description:
      name: postgres_pool
      sha256: "589b7aceb97e061db7e78aff6d6e988320aa2a43f232340b51aa2d9b922acacc"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.6"
  redis:
    dependency: transitive
    description:
      name: redis
      sha256: "32e28eb1ba2e0fe2af50bbd06e675e4dfdce4f0ba95c5bc885c72383a1b0b47e"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  retry:
    dependency: transitive
    description:
      name: retry
      sha256: "822e118d5b3aafed083109c72d5f484c6dc66707885e07c0fbcb8b986bba7efc"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  sasl_scram:
    dependency: transitive
    description:
      name: sasl_scram
      sha256: a47207a436eb650f8fdcf54a2e2587b850dc3caef9973ce01f332b07a6fc9cb9
      url: "https://pub.dev"
    source: hosted
    version: "0.1.1"
  saslprep:
    dependency: transitive
    description:
      name: saslprep
      sha256: "79c9e163a82f55da542feaf0f7a59031e74493299c92008b2b404cd88d639bb4"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  serverpod:
    dependency: "direct main"
    description:
      name: serverpod
      sha256: "0e187a03a7f97205fb415523ae18b96b683dafe52345d8c2248f2dc343a7040a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  serverpod_client:
    dependency: transitive
    description:
      name: serverpod_client
      sha256: "27bf49f7f24c029270623030cd9f120f3be99c36c56cb5dac976e5aa78509f28"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  serverpod_cloud_storage_s3:
    dependency: "direct main"
    description:
      name: serverpod_cloud_storage_s3
      sha256: c1c5d49fda389d0eb1916c11fe4f5b3a0763a15deee3bdcfd8371545d0b49b55
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  serverpod_serialization:
    dependency: transitive
    description:
      name: serverpod_serialization
      sha256: f4ccf17387f6a5a251a0234acca77ce1cb22212987987637668876c394929cb7
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  serverpod_service_client:
    dependency: transitive
    description:
      name: serverpod_service_client
      sha256: ac4e195d651c2f4d88656eadcc30ebabae28b2f79320a38788d2a41a0f77d5fb
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  serverpod_shared:
    dependency: transitive
    description:
      name: serverpod_shared
      sha256: f48291bb41fe0cb397374c73bfd463ed44396502af31e3b3df50b1686170fcc4
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  super_string:
    dependency: transitive
    description:
      name: super_string
      sha256: ba41acf9fbb318b3fc0d57c9235779100394d85d83f45ab533615df1f3146ea7
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  system_resources:
    dependency: transitive
    description:
      name: system_resources
      sha256: "7e78cf8ef224c1e0b6bf4462322b6739fea2274fef928cbcbf21ae5e5a8272d8"
      url: "https://pub.dev"
    source: hosted
    version: "1.6.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  unorm_dart:
    dependency: transitive
    description:
      name: unorm_dart
      sha256: "5b35bff83fce4d76467641438f9e867dc9bcfdb8c1694854f230579d68cd8f4b"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: c538be99af830f478718b51630ec1b6bee5e74e52c8a802d328d9e71d35d2583
      url: "https://pub.dev"
    source: hosted
    version: "11.10.0"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: af5e77e9b83f2f4adc5d3f0a4ece1c7f45a2467b695c2540381bac793e34e556
      url: "https://pub.dev"
    source: hosted
    version: "6.4.2"
  xml2json:
    dependency: transitive
    description:
      name: xml2json
      sha256: c8cb35b83cce879c2ea86951fd257f4e765b0030a0298b35cf94f2b3d0f32095
      url: "https://pub.dev"
    source: hosted
    version: "5.3.6"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=3.0.0 <4.0.0"
