/* AUTOMATICALLY GENERATED CODE DO NOT MODIFY */
/*   To generate run: "serverpod generate"    */

// ignore_for_file: library_private_types_in_public_api
// ignore_for_file: public_member_api_docs
// ignore_for_file: implementation_imports

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:serverpod/serverpod.dart' as _i1;

class PurposeSubCategory extends _i1.TableRow {
  PurposeSubCategory({
    int? id,
    required this.purpose_category_id,
    required this.purpose_sub_category_name,
    required this.purpose_img,
  }) : super(id);

  factory PurposeSubCategory.fromJson(
    Map<String, dynamic> jsonSerialization,
    _i1.SerializationManager serializationManager,
  ) {
    return PurposeSubCategory(
      id: serializationManager.deserialize<int?>(jsonSerialization['id']),
      purpose_category_id: serializationManager
          .deserialize<int>(jsonSerialization['purpose_category_id']),
      purpose_sub_category_name: serializationManager
          .deserialize<String>(jsonSerialization['purpose_sub_category_name']),
      purpose_img: serializationManager
          .deserialize<String>(jsonSerialization['purpose_img']),
    );
  }

  static final t = PurposeSubCategoryTable();

  int purpose_category_id;

  String purpose_sub_category_name;

  String purpose_img;

  @override
  String get tableName => 'purpose_sub_category';

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'purpose_category_id': purpose_category_id,
      'purpose_sub_category_name': purpose_sub_category_name,
      'purpose_img': purpose_img,
    };
  }

  @override
  Map<String, dynamic> toJsonForDatabase() {
    return {
      'id': id,
      'purpose_category_id': purpose_category_id,
      'purpose_sub_category_name': purpose_sub_category_name,
      'purpose_img': purpose_img,
    };
  }

  @override
  Map<String, dynamic> allToJson() {
    return {
      'id': id,
      'purpose_category_id': purpose_category_id,
      'purpose_sub_category_name': purpose_sub_category_name,
      'purpose_img': purpose_img,
    };
  }

  @override
  void setColumn(
    String columnName,
    value,
  ) {
    switch (columnName) {
      case 'id':
        id = value;
        return;
      case 'purpose_category_id':
        purpose_category_id = value;
        return;
      case 'purpose_sub_category_name':
        purpose_sub_category_name = value;
        return;
      case 'purpose_img':
        purpose_img = value;
        return;
      default:
        throw UnimplementedError();
    }
  }

  static Future<List<PurposeSubCategory>> find(
    _i1.Session session, {
    PurposeSubCategoryExpressionBuilder? where,
    int? limit,
    int? offset,
    _i1.Column? orderBy,
    List<_i1.Order>? orderByList,
    bool orderDescending = false,
    bool useCache = true,
    _i1.Transaction? transaction,
  }) async {
    return session.db.find<PurposeSubCategory>(
      where: where != null ? where(PurposeSubCategory.t) : null,
      limit: limit,
      offset: offset,
      orderBy: orderBy,
      orderByList: orderByList,
      orderDescending: orderDescending,
      useCache: useCache,
      transaction: transaction,
    );
  }

  static Future<PurposeSubCategory?> findSingleRow(
    _i1.Session session, {
    PurposeSubCategoryExpressionBuilder? where,
    int? offset,
    _i1.Column? orderBy,
    bool orderDescending = false,
    bool useCache = true,
    _i1.Transaction? transaction,
  }) async {
    return session.db.findSingleRow<PurposeSubCategory>(
      where: where != null ? where(PurposeSubCategory.t) : null,
      offset: offset,
      orderBy: orderBy,
      orderDescending: orderDescending,
      useCache: useCache,
      transaction: transaction,
    );
  }

  static Future<PurposeSubCategory?> findById(
    _i1.Session session,
    int id,
  ) async {
    return session.db.findById<PurposeSubCategory>(id);
  }

  static Future<int> delete(
    _i1.Session session, {
    required PurposeSubCategoryExpressionBuilder where,
    _i1.Transaction? transaction,
  }) async {
    return session.db.delete<PurposeSubCategory>(
      where: where(PurposeSubCategory.t),
      transaction: transaction,
    );
  }

  static Future<bool> deleteRow(
    _i1.Session session,
    PurposeSubCategory row, {
    _i1.Transaction? transaction,
  }) async {
    return session.db.deleteRow(
      row,
      transaction: transaction,
    );
  }

  static Future<bool> update(
    _i1.Session session,
    PurposeSubCategory row, {
    _i1.Transaction? transaction,
  }) async {
    return session.db.update(
      row,
      transaction: transaction,
    );
  }

  static Future<void> insert(
    _i1.Session session,
    PurposeSubCategory row, {
    _i1.Transaction? transaction,
  }) async {
    return session.db.insert(
      row,
      transaction: transaction,
    );
  }

  static Future<int> count(
    _i1.Session session, {
    PurposeSubCategoryExpressionBuilder? where,
    int? limit,
    bool useCache = true,
    _i1.Transaction? transaction,
  }) async {
    return session.db.count<PurposeSubCategory>(
      where: where != null ? where(PurposeSubCategory.t) : null,
      limit: limit,
      useCache: useCache,
      transaction: transaction,
    );
  }
}

typedef PurposeSubCategoryExpressionBuilder = _i1.Expression Function(
    PurposeSubCategoryTable);

class PurposeSubCategoryTable extends _i1.Table {
  PurposeSubCategoryTable() : super(tableName: 'purpose_sub_category');

  /// The database id, set if the object has been inserted into the
  /// database or if it has been fetched from the database. Otherwise,
  /// the id will be null.
  final id = _i1.ColumnInt('id');

  final purpose_category_id = _i1.ColumnInt('purpose_category_id');

  final purpose_sub_category_name =
      _i1.ColumnString('purpose_sub_category_name');

  final purpose_img = _i1.ColumnString('purpose_img');

  @override
  List<_i1.Column> get columns => [
        id,
        purpose_category_id,
        purpose_sub_category_name,
        purpose_img,
      ];
}

@Deprecated('Use PurposeSubCategoryTable.t instead.')
PurposeSubCategoryTable tPurposeSubCategory = PurposeSubCategoryTable();
