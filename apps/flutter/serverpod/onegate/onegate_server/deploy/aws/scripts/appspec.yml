version: 0.0
os: linux
files:
  - source: /vendor/
    destination: /home/<USER>/serverpod/upload/vendor/
  - source: /onegate_server/
    destination: /home/<USER>/serverpod/upload/onegate_server/
hooks:
  BeforeInstall:
    - location: onegate_server/deploy/aws/scripts/install_dependencies
      timeout: 300
      runas: root
  ApplicationStart:
    - location: onegate_server/deploy/aws/scripts/start_server
      timeout: 300
      runas: root
