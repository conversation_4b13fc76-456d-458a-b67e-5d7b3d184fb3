#!/bin/bash

# Make sure permissions are correct for the serverpod directory
chown -R ec2-user:ec2-user /home/<USER>/serverpod

# Run pub get as ec2-user
cd /home/<USER>/serverpod/upload/onegate_server/
sudo -u ec2-user /usr/lib/dart/bin/dart pub get

# Set correct permissions for start script
chmod 755 deploy/aws/scripts/run_serverpod

# Stop the server if it's running, copy files, and restart
systemctl stop serverpod

rm -rf /home/<USER>/serverpod/active/
cp -rp /home/<USER>/serverpod/upload/ /home/<USER>/serverpod/active/

systemctl start serverpod
