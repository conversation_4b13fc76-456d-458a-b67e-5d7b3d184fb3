module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "2.77.0"

  name = "${var.project_name}-vpc"
  cidr = "10.0.0.0/16"

  azs                  = data.aws_availability_zones.available.names
  public_subnets       = ["********/24", "********/24", "********/24"]
  enable_dns_hostnames = true
  enable_dns_support   = true

  # create_database_subnet_group           = true
  # create_database_subnet_route_table     = true
  # create_database_internet_gateway_route = true

  # create_elasticache_subnet_group = true
}
