/* AUTOMA<PERSON>CALL<PERSON> GENERATED CODE DO NOT MODIFY */
/*   To generate run: "serverpod generate"    */

// ignore_for_file: library_private_types_in_public_api
// ignore_for_file: public_member_api_docs
// ignore_for_file: implementation_imports

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:serverpod_client/serverpod_client.dart' as _i1;

class StaffSubCategory extends _i1.SerializableEntity {
  StaffSubCategory({
    this.id,
    required this.staff_category_id,
    required this.sub_categories_name,
  });

  factory StaffSubCategory.fromJson(
    Map<String, dynamic> jsonSerialization,
    _i1.SerializationManager serializationManager,
  ) {
    return StaffSubCategory(
      id: serializationManager.deserialize<int?>(jsonSerialization['id']),
      staff_category_id: serializationManager
          .deserialize<int>(jsonSerialization['staff_category_id']),
      sub_categories_name: serializationManager
          .deserialize<String>(jsonSerialization['sub_categories_name']),
    );
  }

  /// The database id, set if the object has been inserted into the
  /// database or if it has been fetched from the database. Otherwise,
  /// the id will be null.
  int? id;

  int staff_category_id;

  String sub_categories_name;

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'staff_category_id': staff_category_id,
      'sub_categories_name': sub_categories_name,
    };
  }
}
