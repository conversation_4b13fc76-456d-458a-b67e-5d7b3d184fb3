/* AUTOMATICALL<PERSON> GENERATED CODE DO NOT MODIFY */
/*   To generate run: "serverpod generate"    */

// ignore_for_file: library_private_types_in_public_api
// ignore_for_file: public_member_api_docs
// ignore_for_file: implementation_imports

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:serverpod_client/serverpod_client.dart' as _i1;

class Visitor extends _i1.SerializableEntity {
  Visitor({
    this.id,
    required this.name,
    required this.mobile,
    required this.visitor_image,
  });

  factory Visitor.fromJson(
    Map<String, dynamic> jsonSerialization,
    _i1.SerializationManager serializationManager,
  ) {
    return Visitor(
      id: serializationManager.deserialize<int?>(jsonSerialization['id']),
      name: serializationManager.deserialize<String>(jsonSerialization['name']),
      mobile:
          serializationManager.deserialize<String>(jsonSerialization['mobile']),
      visitor_image: serializationManager
          .deserialize<String>(jsonSerialization['visitor_image']),
    );
  }

  /// The database id, set if the object has been inserted into the
  /// database or if it has been fetched from the database. Otherwise,
  /// the id will be null.
  int? id;

  String name;

  String mobile;

  String visitor_image;

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mobile': mobile,
      'visitor_image': visitor_image,
    };
  }
}
