<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="304a6741-b6fd-447d-9782-f04873f06171" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/serverpod/onegate/onegate_client/lib/src/protocol/client.dart" beforeDir="false" afterPath="$PROJECT_DIR$/serverpod/onegate/onegate_client/lib/src/protocol/client.dart" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="GitLabMergeRequestFiltersHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPENED",
    "assignee": {
      "type": "org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue",
      "username": "ninad.nyayanirgune",
      "fullname": "Ninad Nyayanirgune"
    }
  }
}]]></component>
  <component name="GitLabMergeRequestsSettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "first": "https://gitrepo.futurescapetech.com/app-team/one-monorepo.git",
    "second": "43b640aa-e8a9-4731-a632-eb5f25b6974b"
  }
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2nkMh8kx08jeP23KTbI3oZHWn20" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "dart.analysis.tool.window.visible": "false",
    "git-widget-placeholder": "stage__1",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "/Users/<USER>/Desktop/one-monorepo/apps/flutter",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="304a6741-b6fd-447d-9782-f04873f06171" name="Changes" comment="" />
      <created>1729512742083</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1729512742083</updated>
    </task>
    <servers />
  </component>
</project>