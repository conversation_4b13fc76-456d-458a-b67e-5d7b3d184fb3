{"name": "flutter-commons-theme-manager", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/flutter/commons/theme-manager/src", "projectType": "library", "targets": {"analyze": {"executor": "nx:run-commands", "options": {"command": "flutter analyze", "cwd": "libs/flutter/commons/theme-manager"}}, "clean": {"executor": "nx:run-commands", "options": {"command": "flutter clean", "cwd": "libs/flutter/commons/theme-manager"}}, "format": {"executor": "nx:run-commands", "options": {"command": "flutter format libs/flutter/commons/theme-manager/*", "cwd": "libs/flutter/commons/theme-manager"}}, "test": {"executor": "nx:run-commands", "options": {"command": "flutter test", "cwd": "libs/flutter/commons/theme-manager"}}, "doctor": {"executor": "nx:run-commands", "options": {"command": "flutter doctor", "cwd": "libs/flutter/commons/theme-manager"}}, "build-aar": {"executor": "nx:run-commands", "options": {"command": "flutter build aar", "cwd": "libs/flutter/commons/theme-manager"}, "outputs": ["{workspaceRoot}/libs/flutter/commons/theme-manager/build"]}, "build-apk": {"executor": "nx:run-commands", "options": {"command": "flutter build apk", "cwd": "libs/flutter/commons/theme-manager"}, "outputs": ["{workspaceRoot}/libs/flutter/commons/theme-manager/build"]}, "build-appbundle": {"executor": "nx:run-commands", "options": {"command": "flutter build appbundle", "cwd": "libs/flutter/commons/theme-manager"}, "outputs": ["{workspaceRoot}/libs/flutter/commons/theme-manager/build"]}, "build-bundle": {"executor": "nx:run-commands", "options": {"command": "flutter build bundle", "cwd": "libs/flutter/commons/theme-manager"}, "outputs": ["{workspaceRoot}/libs/flutter/commons/theme-manager/build"]}, "build-ios": {"executor": "nx:run-commands", "options": {"command": "flutter build ios", "cwd": "libs/flutter/commons/theme-manager"}, "outputs": ["{workspaceRoot}/libs/flutter/commons/theme-manager/build"]}, "build-ios-framework": {"executor": "nx:run-commands", "options": {"command": "flutter build ios-framework", "cwd": "libs/flutter/commons/theme-manager"}, "outputs": ["{workspaceRoot}/libs/flutter/commons/theme-manager/build"]}, "build-ipa": {"executor": "nx:run-commands", "options": {"command": "flutter build ipa", "cwd": "libs/flutter/commons/theme-manager"}, "outputs": ["{workspaceRoot}/libs/flutter/commons/theme-manager/build"]}}, "tags": []}