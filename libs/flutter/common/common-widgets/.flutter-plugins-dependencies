{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "native_build": true, "dependencies": []}], "android": [{"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "native_build": true, "dependencies": []}], "macos": [{"name": "speech_to_text_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text_macos-1.1.0/", "native_build": true, "dependencies": []}], "linux": [], "windows": [], "web": [{"name": "speech_to_text", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/speech_to_text-6.6.2/", "dependencies": []}]}, "dependencyGraph": [{"name": "speech_to_text", "dependencies": ["speech_to_text_macos"]}, {"name": "speech_to_text_macos", "dependencies": []}], "date_created": "2025-03-04 13:08:35.902015", "version": "3.27.3", "swift_package_manager_enabled": false}