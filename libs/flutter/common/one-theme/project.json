{"name": "flutter-common-one-theme", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/flutter/common/one-theme/src", "projectType": "library", "targets": {"analyze": {"executor": "nx:run-commands", "options": {"command": "fvm flutter analyze", "cwd": "libs/flutter/common/one-theme"}}, "clean": {"executor": "nx:run-commands", "options": {"command": "fvm flutter clean", "cwd": "libs/flutter/common/one-theme"}}, "format": {"executor": "nx:run-commands", "options": {"command": "fvm flutter format libs/flutter/common/one-theme/*", "cwd": "libs/flutter/common/one-theme"}}, "test": {"executor": "nx:run-commands", "options": {"command": "fvm flutter test", "cwd": "libs/flutter/common/one-theme"}}, "doctor": {"executor": "nx:run-commands", "options": {"command": "fvm flutter doctor", "cwd": "libs/flutter/common/one-theme"}}, "build-aar": {"executor": "nx:run-commands", "options": {"command": "fvm flutter build aar", "cwd": "libs/flutter/common/one-theme"}, "outputs": ["{workspaceRoot}/libs/flutter/common/one-theme/build"]}, "build-apk": {"executor": "nx:run-commands", "options": {"command": "fvm flutter build apk", "cwd": "libs/flutter/common/one-theme"}, "outputs": ["{workspaceRoot}/libs/flutter/common/one-theme/build"]}, "build-appbundle": {"executor": "nx:run-commands", "options": {"command": "fvm flutter build appbundle", "cwd": "libs/flutter/common/one-theme"}, "outputs": ["{workspaceRoot}/libs/flutter/common/one-theme/build"]}, "build-bundle": {"executor": "nx:run-commands", "options": {"command": "fvm flutter build bundle", "cwd": "libs/flutter/common/one-theme"}, "outputs": ["{workspaceRoot}/libs/flutter/common/one-theme/build"]}, "build-ios": {"executor": "nx:run-commands", "options": {"command": "fvm flutter build ios", "cwd": "libs/flutter/common/one-theme"}, "outputs": ["{workspaceRoot}/libs/flutter/common/one-theme/build"]}, "build-ios-framework": {"executor": "nx:run-commands", "options": {"command": "fvm flutter build ios-framework", "cwd": "libs/flutter/common/one-theme"}, "outputs": ["{workspaceRoot}/libs/flutter/common/one-theme/build"]}, "build-ipa": {"executor": "nx:run-commands", "options": {"command": "fvm flutter build ipa", "cwd": "libs/flutter/common/one-theme"}, "outputs": ["{workspaceRoot}/libs/flutter/common/one-theme/build"]}}, "tags": []}